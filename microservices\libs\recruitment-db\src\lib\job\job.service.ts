import { BadRequestException, ConflictException, HttpException, HttpStatus, Injectable, InternalServerErrorException, Logger } from "@nestjs/common";
import { JobDto } from "./dto/job.dto";
import { InjectModel } from "@nestjs/sequelize";
import { Jobs } from "./job.model";
import { Company } from "../companies/companies.model";
import { User } from "../users/users.model";
import { UpdateJobDto } from "./dto/update-job.dto";
import {
  FilterCompanyJobsDto,
  FilterJobDto,
  FilterPublicCompanyJobsDto,
  SingleJobFilterDto,
} from "./dto/filter-job.dto";
import { Op, QueryTypes } from "sequelize";
import { Location } from "../locations/location.model";
import { Industry } from "../industries/industries.model";
import { Workflow } from "../workflow/workflow.model";
import { Position } from "../positions/positions.model";
import { Subscribe } from "../subscribe/subscribe.model";
import { GoogleService } from "@microservices/integrations";
import { EmailService } from "@microservices/email";
import { HttpService } from "@nestjs/axios";
import { Candidate } from "../candidates/candidates.model";
import { Skill } from "../skills/skills.model";
import { SubscriptionService } from "../subscription/subscription.service";
import { connectToTemporal } from "apps/temporal/src/app/workflow/workflow.provider";
import { jobClosedSignal } from "apps/temporal/src/app/workflow/temporal/workflows";
import { WorkflowService } from "../workflow/workflow.service";
import { getDummyJobs } from "../utils/fillDummyData";
import { putRecordOnQdrant } from '@microservices/qdrant'
import { ForeignKeyConstraintError } from 'sequelize';
import { JobTargetService } from "../jobtarget/jobtarget.service";

const workflowType = {
  "Domain Assessment": {
    "id": 2,
    "name": "Functional/Domain Assessment",
    "settings": true
  },
  "live-task": {
    "id": 3,
    "name": "Live Task/Coding Assessment",
    "settings": true
  },
  "take-home": {
    "id": 4,
    "name": "Take Home/Coding Assessment",
    "settings": true
  }
}


@Injectable()
export class JobService {
  constructor(
    @InjectModel(Jobs) private jobRepository: typeof Jobs,
    @InjectModel(Candidate) private candidateRepository: typeof Candidate,
    private googleService: GoogleService,
    private emailService: EmailService,
    private workflowService: WorkflowService,
    private readonly httpService: HttpService,
    private readonly jobTargetService: JobTargetService
  ) { }

  async createJob(dto: JobDto, companyId: number, authorId: number, authHeader = '') {
    try {
      const { locations, ...jobData } = dto;

      // @ts-ignore
      const job = await this.jobRepository.create({
        ...jobData,
        companyId: companyId,
        authorId: authorId,
      }, {
        include: [Company, Location, Industry, Workflow]
      });

      if (locations?.length) {
        const locationIds = locations.map(loc => loc.id);
        await job.$set('locations', locationIds);
      }


      if (dto.approvalEmail) {
        await this.emailService.sendRecruitment(
          "approval.html",
          { userName: dto.approvalName, userId: dto.approverId, jobTitle: job.title, companyName: job.company.name },
          dto.approvalEmail,
          `${job.title} is waiting for approval`,
          {
            notificationTitle: `Job Approval Pending for ${job.title}`,
            notificationMsg: `You’ve been assigned to review and approve the job posting for ${job.title} at ${job.company.name}. Please review and approve it to publish.`
          }
        );
      }
      const jobCreated = await this.getJob(job.id, {}, 'publish');
      await this.createJobInBoards(jobCreated, 'googleJobs08122022');
      await createAssignment(jobCreated, job, this.httpService, authHeader);
      const recordData = this.getJobDataForQdrant(job);
      return await this.addDataonOpensearch(jobCreated.id, 'CREATION');
      // return { response: jobCreated, recordData };
    } catch (err) {
      Logger.log("Error while creating a job", err)
    }
  }

  async addDataonOpensearch(jobId, actionType: 'CREATION' | 'UPDATE') {
    const jobCreated = await this.getJob(jobId);
    if (jobCreated && !jobCreated.isAssessment) {
      const recordData = this.getJobsRecordData(jobCreated, actionType);
      return { response: jobCreated, recordData };
    }
  }

  getJobData = async (id: number) => {
    const jobs = await this.jobRepository.findOne({
      where: {
        id,
      },
      include: [{
        model: User,
        as: "author",
        attributes: ["id", "firstname", "lastname", "email"]
      },
        Company, Location]
    });

    return jobs;
  };

  getJobBoardsService(uniqueIntegrationId) {
    let jobBoardsService;
    switch (uniqueIntegrationId) {
      case "googleJobs08122022":
        jobBoardsService = this.googleService;
        break;
      case "jobtarget78952158":
        jobBoardsService = this.jobTargetService;
        break;
      default:
        jobBoardsService = false;
    }

    return jobBoardsService;
  }

  async createJobInBoards(job, uniqueIntegrationId?) {
    if (!job?.jobBoards?.length) return;

    const jobBoards = job.jobBoards;
    let saveToDb = false;

    for (let i = 0; i < jobBoards.length; i++) {
      if (jobBoards[i].uniqueIntegrationId) {

        const jobBoardsService = this.getJobBoardsService(
          jobBoards[i].uniqueIntegrationId
        );
        if (jobBoardsService) {
          const data = await jobBoardsService.createJob(job);
          if (data) {
            jobBoards[i].data = data;
            saveToDb = true;
          }
        }
      }
    }
  }

  async createDummy(dto) {
    const company = dto.company;
    if (company && company?.name && company?.id && company?.industry) {
      const jobs = getDummyJobs(company.id, company.ownerId, company.name);
      for (const job of jobs) {
        try {
          const newJob = await this.createJob(job, company.id, company.ownerId);
          if (newJob?.recordData) {
            putRecordOnQdrant([newJob.recordData]);
          }
        } catch (err) {
          console.log("Failed to create dummy job ", { err })
          continue;
        }
      }
    }
  }

  async getAllJobs(dto: FilterJobDto) {
    const where: any = { isAssessment: false };
    if (dto.title) {
      where.title = {
        [Op.iLike]: `%${dto.title}%`,
      };
    }

    if (dto.createdAtFrom && dto.createdAtTo) {
      where.createdAt = {
        [Op.between]: [new Date(dto.createdAtFrom), new Date(dto.createdAtTo)],
      };
    } else if (dto.createdAtFrom || dto.createdAtTo) {
      if (dto.createdAtFrom) {
        where.createdAt = {
          [Op.gte]: new Date(dto.createdAtFrom),
        };
      }
      if (dto.createdAtTo) {
        where.createdAt = {
          [Op.lte]: new Date(dto.createdAtTo),
        };
      }
    }

    if (dto.salaryMonthMin) {
      where.salaryMonthMin = {
        [Op.gte]: dto.salaryMonthMin,
      };
    }
    if (dto.salaryMonthMax) {
      where.salaryMonthMax = {
        [Op.lte]: dto.salaryMonthMax,
      };
    }

    if (dto.salaryYearMin) {
      where.salaryYearMin = {
        [Op.gte]: dto.salaryYearMin,
      };
    }
    if (dto.salaryYearMax) {
      where.salaryYearMax = {
        [Op.lte]: dto.salaryYearMax,
      };
    }

    if (dto.salaryHourMin) {
      where.salaryHourMin = {
        [Op.gte]: dto.salaryHourMin,
      };
    }
    if (dto.salaryHourMax) {
      where.salaryHourMax = {
        [Op.lte]: dto.salaryHourMax,
      };
    }

    if (dto.experienceMin) {
      where.experienceMin = {
        [Op.gte]: dto.experienceMin,
      };
    }
    if (dto.experienceMax) {
      where.experienceMax = {
        [Op.lte]: dto.experienceMax,
      };
    }

    if (dto.education) {
      where.education = {
        [Op.iLike]: `%${dto.education}%`,
      };
    }

    if (dto.skills) {
      where.skills = {
        [Op.contains]: dto.skills,
      };
    }

    if (dto.companyId) {
      where.companyId = {
        [Op.eq]: dto.companyId,
      };
    }

    if (dto.jobType) {
      where.jobType = {
        [Op.iLike]: `%${dto.jobType}%`,
      };
    }

    if (dto.preferableShift) {
      where.preferableShift = {
        [Op.iLike]: `%${dto.preferableShift}%`,
      };
    }

    if (dto.industryId) {
      where.industryId = {
        [Op.eq]: dto.industryId,
      };
    }

    if (dto.functionalArea) {
      where.functionalArea = {
        [Op.iLike]: `%${dto.functionalArea}%`,
      };
    }

    where.status = {
      [Op.or]: ["closed", "publish"]
    };

    const whereLoc: any = {};
    let requiredLoc = false;
    if (dto.locations) {
      requiredLoc = true;
      if (dto.locations instanceof Array) {
        whereLoc.id = {
          [Op.in]: dto.locations,
        };
      } else {
        whereLoc.id = {
          [Op.eq]: dto.locations,
        };
      }
    }
    const include: any = [
      {
        model: Company,
        attributes: ["id", "name", "tenantId", "avatar"],
      },
      {
        model: Location,
        where: whereLoc,
        attributes: ["id", "city", "state"],
        required: requiredLoc,
      },
      {
        model: Industry,
        attributes: ["id", "value", "label"],
      },
    ];
    if (dto.currentUserId) {
      const subscribeWhere: any = {
        userId: dto.currentUserId,
      };
      let subscribeRequired = false;
      if (dto.filterType == "matched") {
        subscribeWhere.match = {
          [Op.gte]: 0,
        };
        subscribeRequired = true;
      } else if (dto.filterType == "saved") {
        subscribeWhere.saveJob = true;
        subscribeRequired = true;
      } else if (dto.filterType == "applied") {
        subscribeWhere.applyJob = true;
        subscribeRequired = true;
      }
      include.push({
        model: Subscribe,
        required: subscribeRequired,
        where: subscribeWhere,
        attributes: [
          "id",
          "userId",
          "jobId",
          "match",
          "saveJob",
          "applyJob",
          "subscribeJob",
        ],
      });
    }
    const order = [];
    if (dto.sortBy && dto.sortType) {
      order.push([dto.sortBy, dto.sortType]);
    } else {
      order.push(["createdAt", "DESC"]);
    }
    const data = await this.jobRepository.findAndCountAll({
      where,
      attributes: [
        "id",
        "title",
        "remoteLocation",
        "negotiable",
        "benefits",
        "education",
        "description",
        "workflowId",
        "companyId",
        "authorId",
        "approverId",
        "numberOpenings",
        "positionId",
        "industryId",
        "shortDescription",
        "experienceMin",
        "experienceMax",
        "salaryYearMin",
        "salaryYearMax",
        "salaryHourMin",
        "salaryHourMax",
        "salaryMonthMin",
        "salaryMonthMax",
        "education",
        "jobType",
        "preferableShift",
        "functionalArea",
        "skills",
        "createdAt",
        "applicationForm",
        "status",
        "employer"
      ],
      include,
      order,
      limit: dto.limit,
      offset: dto.offset,
      distinct: true,
    });
    return data
  }

  async getJob(id: number, query: SingleJobFilterDto = {}, status: string = '') {
    const include: any = [
      {
        model: Company,
        attributes: ["id", "name", "tenantId", "avatar", "website"],
      },
      {
        model: Location,
        attributes: ["id", "city", "state", "st"],
      },
      {
        model: Industry,
        attributes: ["id", "value", "label"],
      },
      {
        model: Workflow,
      }
    ];
    if (query.currentUserId) {
      include.push({
        model: Subscribe,
        required: false,
        where: {
          userId: query.currentUserId,
        },
        attributes: [
          "id",
          "userId",
          "jobId",
          "match",
          "saveJob",
          "applyJob",
          "subscribeJob",
        ],
      });
    }
    return await this.jobRepository.findOne({
      where: {
        id,
        ...(status && {
          status: {
            [Op.iLike]: `%${status}%`,
          },
        })
      },
      include,
    });
  }

  async getCompanyJob(id: number, companyId: number) {
    return await this.jobRepository.findOne({
      where: {
        id,
        companyId,
        isAssessment: false,
      },
      include: [
        {
          model: User,
          as: "author",
          attributes: ["id", "firstname", "lastname"],
        },
        {
          model: Location,
          attributes: ["id", "city", "state"],
        },
        {
          model: Industry,
          attributes: ["id", "value", "label"],
        },
        {
          model: Workflow,
          attributes: ["id", "title"],
        },
        {
          model: Position,
          attributes: ["id", "value", "label"],
        },
        {
          model: Company,
          attributes: ["name"],
        },
      ],
    });
  }

  async editJob(dto: UpdateJobDto, companyId: number, authHeader) {
    const job = await this.jobRepository.findOne({
      where: { id: dto.id, companyId },
      include: ["locations", Company, Workflow],
    });
    if (job) {
      if (dto.locations) {
        const remove = [];
        const add = [];
        for (const item of job.locations) {
          const exist = dto.locations.find((x) => x.id === item.id);
          if (!exist) {
            remove.push(item.id);
          }
        }
        for (const item of dto.locations) {
          const existRole = job.locations.find((x) => x.id === item.id);
          if (!existRole) {
            add.push(item.id);
          }
        }
        await job.$add("locations", add);
        await job.$remove("locations", remove);
      }
      const jobBoardsPrev = job.jobBoards;
      Object.assign(job, {
        ...dto,
        authorId: job.authorId,
        companyId: job.companyId,
      });
      await job.save();
      try {
        await this.updateJobInBoards(job.id, companyId, jobBoardsPrev);
      } catch (error) {
        Logger.log("Error updating job in boards", error);
      }
      if (dto.status === 'closed') {
        const url = `${process.env.RECRUITMENT_API_URI}/api/temporal-workflow/jobs?jobId=${job.id}`;
        const data = await this.httpService.get(url, authHeader).toPromise();

        if (data && data.data && data.data?.count) {
          const workflows = data.data?.rows;
          const client = await connectToTemporal();

          if (client) {
            for (let workflow of workflows) {
              try {
                const handle = await client.getHandle(workflow?.workflowid);
                const description = await handle.describe();

                if (description.status.name === 'RUNNING') {
                  try {
                    await handle.signal(jobClosedSignal, {
                      data: "Job Closed"
                    });
                  } catch (error) {
                    console.log("Error when sending signal:", error);
                    continue;
                  }
                } else {
                  Logger.log("Workflow is not in running state for", handle.workflowId);
                }
              } catch (error) {
                Logger.log(`Error retrieving workflow ${workflow?.workflowid}:`, error);
                // Workflow may not exist or may have completed
                continue;
              }
            }
          }
        }

        try {
          if (job.jobTargetId || job.jobBoards?.some(board => board.uniqueIntegrationId === 'jobtarget78952158')) {
            const jobTargetResult = await this.jobTargetService.closeJob(job);
            if (jobTargetResult?.success) {
              Logger.log(`JobTarget job ${job.jobTargetId || job.id} closed successfully`);
            } else {
              Logger.warn(`Failed to close JobTarget job: ${jobTargetResult?.message || 'Unknown error'}`);
            }
          }
        } catch (error) {
          Logger.error(`Error closing job in JobTarget: ${error.message}`, error.stack);
        }
      }
      if (dto.approvalEmail) {
        await this.emailService.sendRecruitment(
          "approval.html",
          { userName: dto.approvalName, userId: dto.approverId, jobTitle: job.title, companyName: job.company.name },
          dto.approvalEmail,
          `${job.title} is waiting for approval`,
          {
            notificationTitle: `Job Approval Pending for ${job.title}`,
            notificationMsg: `You’ve been assigned to review and approve the job posting for ${job.title} at ${job.company.name}. Please review and approve it to publish.`
          }
        );
      }
      const jobCreated = await this.getJob(job.id);
      await createAssignment(jobCreated, job, this.httpService, authHeader);
      const recordData = this.getJobDataForQdrant(job);
      return await this.addDataonOpensearch(jobCreated.id, 'UPDATE');
    } else {
      throw new HttpException("Access error", HttpStatus.FORBIDDEN);
    }
  }

  async updateJobInBoards(jobId, companyId, jobBoardsPrev) {
    const job = await this.jobRepository.findOne({
      where: { id: jobId, companyId },
      include: ["locations"],
    });

    // remove old jobBoards
    if (jobBoardsPrev?.length) {
      for (let i = 0; i < jobBoardsPrev.length; i++) {
        if (
          !job.jobBoards.find(
            (item) =>
              item.uniqueIntegrationId === jobBoardsPrev[i].uniqueIntegrationId
          )
        ) {
          await this.deleteJobInBoards(job, [jobBoardsPrev[i]]);
        }
      }
    }

    // add new JobBoards
    if (job?.jobBoards?.length && job.status === "publish") {
      for (let i = 0; i < job.jobBoards.length; i++) {
        if (
          !jobBoardsPrev.find(
            (item) =>
              item.uniqueIntegrationId ===
              job.jobBoards[i].uniqueIntegrationId
          )

        ) {
          await this.createJobInBoards(
            job,
            job.jobBoards[i].uniqueIntegrationId
          );
        }
      }
    }

    // update existing jobBoards
    if (jobBoardsPrev?.length && job?.jobBoards?.length) {
      for (let i = 0; i < jobBoardsPrev.length; i++) {
        if (
          job.jobBoards.find(
            (item) =>
              item.uniqueIntegrationId === jobBoardsPrev[i].uniqueIntegrationId
          )
        ) {
          const jobBoardsService = this.getJobBoardsService(jobBoardsPrev[i].uniqueIntegrationId);
          if (jobBoardsService) {
            await jobBoardsService.updateJob(job, jobBoardsPrev[i].data);
          }
        }
      }
    }
  }

  async getAllCompanyJobs(dto: FilterCompanyJobsDto, companyId: number) {
    const where: any = {
      companyId,
      isAssessment: false,
    };
    const order = [];
    if (dto.search) {
      where.title = {
        [Op.iLike]: `%${dto.search}%`,
      };
    }
    if (dto.status) {
      if (Array.isArray(dto.status)) {
        where.status = {
          [Op.any]: dto.status
        }
      } else {
        where.status = {
          [Op.iLike]: `%${dto.status}%`,
        };
      }
    }

    if (dto.dateFrom && dto.dateTo) {
      where.createdAt = {
        [Op.between]: [new Date(dto.dateFrom), new Date(dto.dateTo)],
      };
    } else if (dto.dateFrom || dto.dateTo) {
      if (dto.dateFrom) {
        where.createdAt = {
          [Op.gte]: new Date(dto.dateFrom),
        };
      }
      if (dto.dateTo) {
        where.createdAt = {
          [Op.lte]: new Date(dto.dateTo),
        };
      }
    }

    if (dto.sortBy && dto.sortType) {
      order.push([dto.sortBy, dto.sortType]);
    } else {
      order.push(["id", "DESC"]);
    }

    return await this.jobRepository.findAndCountAll({
      limit: dto.limit,
      offset: dto.offset,
      distinct: true,
      where,
      order,
      attributes: [
        "id",
        "title",
        "salaryYearMin",
        "salaryYearMax",
        "salaryMonthMin",
        "salaryMonthMax",
        "salaryHourMin",
        "salaryHourMax",
        "createdAt",
        "status",
        "authorId",
        "approverId",
        "remoteLocation",
        "workflowId",
        "status",
        "numberOpenings",
        "sso_link"
      ],
      include: [
        {
          model: User,
          attributes: ["id", "firstname", "lastname"],
        },
        {
          model: Location,
        },
        {
          model: Workflow,
          attributes: ["id", "title"],
        },
        {
          model: Subscribe,
          attributes: ["id", "userId", "jobId", "saveJob"],
          include: [
            {
              model: User,
              attributes: ["firstname", "lastname", "avatar"]
            }
          ]
        }
      ],
    });
  }

  async getCompanyJobsWeekCount(query, companyId: number) {
    const where: any = {};
    where.companyId = companyId;
    const datePairs = this.getLast7DaysDatePairs();
    const result = [];
    if (query.status) {
      where.status = {
        [Op.iLike]: `%${query.status}%`,
      };
    }
    for (const datePair of datePairs) {
      const count = await this.jobRepository.count({
        where: {
          ...where,
          updatedAt: {
            [Op.between]: [datePair.dateTo, datePair.dateFrom],
          }
        },
      });
      result.push({ date: datePair.dateFrom, count });
    }
    return result;
  }

  getLast7DaysDatePairs() {
    const today = new Date();
    const datePairs = [];

    for (let i = 0; i < 7; i++) {
      const dateFrom = new Date(today);
      dateFrom.setDate(today.getDate() - i);
      const dateTo = new Date(today);
      dateTo.setDate(today.getDate() - i - 1);
      datePairs.push({ dateFrom, dateTo });
    }

    return datePairs;
  };

  async deleteJob(jobId: number, companyId: number) {
    try {

      const job = await this.jobRepository.findOne({
        where: { id: jobId, companyId },
        include: [Company, Workflow]
      });
      await this.deleteJobInBoards(job, job.jobBoards);

      const deletedJob = await this.jobRepository.destroy({
        where: {
          id: jobId,
          companyId,
        },
      });
      const recordData = this.getJobDataForQdrant(job, 'delete');

      return { response: deletedJob, recordData };
    } catch (error) {
      if (error instanceof ForeignKeyConstraintError) {
        throw new ConflictException('Unable to delete this job because it is referenced by other records.');
      }
      throw error;
    }
  }

  async deleteJobInBoards(job, jobBoards) {
    if (!jobBoards?.length) return;

    for (let i = 0; i < jobBoards.length; i++) {
      const jobBoardsService = this.getJobBoardsService(
        jobBoards[i].uniqueIntegrationId
      );
      if (jobBoardsService) {
        await jobBoardsService.deleteJob(job, jobBoards[i].data);
      }
    }
  }

  async getPublicCompanyJob(dto: FilterPublicCompanyJobsDto) {
    const whereLoc: any = {};
    const where: any = { isAssessment: false };
    where.status = {
      [Op.iLike]: `%publish%`,
    };
    if (dto.locations) {
      if (dto.locations instanceof Array) {
        whereLoc.id = {
          [Op.in]: dto.locations,
        };
      } else {
        whereLoc.id = {
          [Op.eq]: dto.locations,
        };
      }
    }
    if (dto.experienceMin) {
      where.experienceMin = {
        [Op.gte]: dto.experienceMin,
      };
    }
    if (dto.experienceMax) {
      where.experienceMax = {
        [Op.lte]: dto.experienceMax,
      };
    }
    if (dto.salaryMonthMin) {
      where.salaryMonthMin = {
        [Op.gte]: dto.salaryMonthMin,
      };
    }
    if (dto.salaryMonthMax) {
      where.salaryMonthMax = {
        [Op.lte]: dto.salaryMonthMax,
      };
    }
    if (dto.salaryYearMin) {
      where.salaryYearMin = {
        [Op.gte]: dto.salaryYearMin,
      };
    }
    if (dto.salaryYearMax) {
      where.salaryYearMax = {
        [Op.lte]: dto.salaryYearMax,
      };
    }

    if (dto.salaryHourMin) {
      where.salaryHourMin = {
        [Op.gte]: dto.salaryHourMin,
      };
    }
    if (dto.salaryHourMax) {
      where.salaryHourMax = {
        [Op.lte]: dto.salaryHourMax,
      };
    }


    if (dto.education) {
      where.education = {
        [Op.iLike]: `%${dto.education}%`,
      };
    }
    if (dto.skills) {
      where.skills = {
        [Op.contains]: dto.skills,
      };
    }

    if (dto.search) {
      where.title = {
        [Op.iLike]: `%${dto.search}%`,
      };
    }

    if (dto.createdAtFrom && dto.createdAtTo) {
      where.createdAt = {
        [Op.between]: [new Date(dto.createdAtFrom), new Date(dto.createdAtTo)],
      };
    } else if (dto.createdAtFrom || dto.createdAtTo) {
      if (dto.createdAtFrom) {
        where.createdAt = {
          [Op.gte]: new Date(dto.createdAtFrom),
        };
      }
      if (dto.createdAtTo) {
        where.createdAt = {
          [Op.lte]: new Date(dto.createdAtTo),
        };
      }
    }
    const include: any = [
      {
        model: Location,
        where: whereLoc,
        attributes: ["id", "city", "state"],
        required: true,
      },
      {
        model: Company,
        where: {
          tenantId: {
            [Op.iLike]: dto.tenantId,
          },
        },
        attributes: [],
        required: true,
      },
    ];

    if (dto.currentUserId) {
      include.push({
        model: Subscribe,
        required: false,
        where: {
          userId: dto.currentUserId,
        },
        attributes: [
          "id",
          "userId",
          "jobId",
          "match",
          "saveJob",
          "applyJob",
          "subscribeJob",
        ],
      });
    }

    const order = [];
    if (dto.sortBy && dto.sortType) {
      order.push([dto.sortBy, dto.sortType]);
    } else {
      order.push(["id", "DESC"]);
    }

    return await this.jobRepository.findAndCountAll({
      where,
      attributes: [
        "id",
        "title",
        "createdAt",
        "experienceMin",
        "experienceMax",
        "salaryMonthMin",
        "salaryMonthMax",
        "salaryYearMin",
        "salaryYearMax",
        "skills",
        "remoteLocation",
        "shortDescription",
        "applicationForm",
        "status"
      ],
      include,
      order,
      limit: dto.limit,
      offset: dto.offset,
      distinct: true,
    });
  }

  async getAllCompanyJobsWithWorkflow(dto: FilterCompanyJobsDto, companyId: number) {
    try {
      const where: any = {
        companyId,
        workflowId: { [Op.ne]: null }
      };

      if (dto.jobId) {
        if (dto.jobId instanceof Array) {
          where['id'] = {
            [Op.in]: dto.jobId,
          };
        } else {
          where['id'] = {
            [Op.eq]: dto.jobId,
          };
        }
      }
      const order = [];
      if (dto.search) {
        where.title = {
          [Op.iLike]: `%${dto.search}%`,
        };
      }
      if (dto.status) {
        where.status = {
          [Op.iLike]: `%${dto.status}%`,
        };
      }

      if (dto.dateFrom && dto.dateTo) {
        where.createdAt = {
          [Op.between]: [new Date(dto.dateFrom), new Date(dto.dateTo)],
        };
      } else if (dto.dateFrom || dto.dateTo) {
        if (dto.dateFrom) {
          where.createdAt = {
            [Op.gte]: new Date(dto.dateFrom),
          };
        }
        if (dto.dateTo) {
          where.createdAt = {
            [Op.lte]: new Date(dto.dateTo),
          };
        }
      }

      if (dto.sortBy && dto.sortType) {
        order.push([dto.sortBy, dto.sortType]);
      } else {
        order.push(["id", "DESC"]);
      }

      const re = await this.jobRepository.findAndCountAll({
        limit: dto.limit,
        offset: dto.offset,
        distinct: true,
        where,
        order,
        attributes: [
          "id",
          "title",
          "salaryYearMin",
          "salaryYearMax",
          "salaryMonthMin",
          "salaryMonthMax",
          "salaryHourMin",
          "salaryHourMax",
          "createdAt",
          "status",
          "authorId",
          "remoteLocation",
          "workflowId",
        ],
        include: [
          {
            model: User,
            attributes: ["id", "firstname", "lastname"],
          },
          {
            model: Location,
          },
          {
            model: Workflow,
            attributes: ["id", "title", "domainId"],
            where: {
              domainId: { [Op.ne]: null }
            }

          },
        ],
      });
      if (re) {
        // Iterate through the jobs and check if at least one has a workflowId
        for (const job of re.rows) {
          if (job.workflowId !== null) {

            return re; // At least one job has a workflowId
          }
        }
      }

      return { count: 0, rows: [] }; // No jobs with a workflowId were found

    } catch (err) {
      console.log(err);

    }
  }

  async getMatchedJobs(dto: FilterJobDto, userId: number) {
    const candidate = await this.candidateRepository.findOne({
      where: { userId: userId },
      include: [
        {
          model: Skill,
          attributes: ["name"],
        },
      ],
    });

    if (!candidate) {
      throw new HttpException('Candidate not found', HttpStatus.NOT_FOUND);
    }

    const skillsCandidate = candidate.skills.map(item => item.name);
    const locationId = candidate?.locationId;
    const remoteLocation = candidate?.remoteLocation;
    const positionId = candidate?.positionId;
    const educationCandidate = escapeString(candidate?.degree);
    const experience = candidate?.experience;
    const salary = candidate?.preferencesExpectedCTC;

    const titleFilter = dto?.title;
    const industryId = dto?.industryId;
    const companyId = dto?.companyId;
    const jobType = dto?.jobType;
    const preferableShift = dto?.preferableShift;
    const functionalArea = dto?.functionalArea;

    const salaryMonthMin = dto?.salaryMonthMin;
    const salaryMonthMax = dto?.salaryMonthMax;
    const salaryYearMin = dto?.salaryYearMin;
    const salaryYearMax = dto?.salaryYearMax;
    const experienceMin = dto?.experienceMin;
    const experienceMax = dto?.experienceMax;

    const createdAtFrom = dto?.createdAtFrom;
    const createdTo = dto?.createdAtTo;

    const education = dto?.education;
    const skills = dto?.skills;

    const locations = dto?.locations;
    const offsetValue = dto?.offset;
    const limitValue = dto?.limit;

    let distinctOn = '(subquery."id"'
    if (dto.sortBy) {
      distinctOn += `,subquery."match_percentage", subquery."${dto.sortBy || 'title'}"`
    }
    distinctOn += ')'
    let query = `
        SELECT DISTINCT ON ${distinctOn} subquery.* ,
        jsonb_build_object('id',c."id",'name',c."name",'tenantId',c."tenantId",'avatar',c."avatar") AS company,
        jsonb_build_array(jsonb_build_object('locationId',subquery."locationId",'city',l."city",'state',l."state")) AS locations,
        jsonb_build_object('industryId',subquery."industryId",'value',i."value",'label',i."label") AS industry,
        jsonb_build_object('id',s."id",'userId',s."userId",'jobId',s."jobId",'match',s."match",'saveJob',s."saveJob",'applyJob',s."applyJob",'subscribeJob',s."subscribeJob") AS subscribes
        from
            (SELECT
                jobs."id",
                jobs."positionId",
                lj2."locationId" as "locationId",
                jobs."authorId",
                jobs."title",
                jobs."employer",
                jobs."consultancy",
                jobs."remoteLocation",
                jobs."salaryMonthMin",
                jobs."salaryMonthMax",
                jobs."salaryYearMin",
                jobs."salaryYearMax",
                jobs."negotiable",
                jobs."description",
                jobs."shortDescription",
                jobs."numberOpenings",
                jobs."jobType",
                jobs."preferableShift",
                jobs."industryId",
                jobs."functionalArea",
                jobs."noticePeriod",
                jobs."skills",
                jobs."experienceMin",
                jobs."experienceMax",
                jobs."education",
                jobs."careerPage",
                jobs."createdAt",
                jobs."companyId",
                jobs."status",
                jobs."isAssessment",
                jobs."applicationForm",
                COALESCE((
                    (   CASE WHEN matched_skills.matched_skill_count IS NULL THEN 0 ELSE matched_skills.matched_skill_count / jsonb_array_length(jobs.skills)::float * 30 END +  
                        CASE WHEN lj2."locationId" = ${locationId} THEN 10 ELSE 0 END +  -- Location points
                        CASE WHEN jobs."remoteLocation" = ${remoteLocation} THEN 5 ELSE 0 END +  -- Remote location points
                        CASE WHEN jobs."positionId" = ${positionId} THEN 20 ELSE 0 END +  -- Position points
                        CASE WHEN jobs."education" = '${educationCandidate}' THEN 10 ELSE 0 END +  -- Education points
                        CASE WHEN (jobs."experienceMin" >= ${experience} AND jobs."experienceMax" <= ${experience}) THEN 10 ELSE 0 END +  -- Experience points
                        CASE WHEN (jobs."salaryYearMin" >= ${salary} AND jobs."salaryYearMax" <= ${salary}) THEN 15 ELSE 0 END  -- Salary points
                    ) / 100.0 * 100), 0) AS match_percentage
            FROM
                jobs
            JOIN location_jobs AS lj2 ON lj2."jobId" = jobs."id"
            LEFT JOIN (
                SELECT
                    "id",
                    COUNT(DISTINCT skill) AS matched_skill_count
                FROM
                    jobs
                CROSS JOIN LATERAL jsonb_array_elements_text(jobs."skills") AS skill
                WHERE
                    skill IN (${skillsCandidate.map(() => '?').join(', ')})  -- Interpolating skills
                GROUP BY
                    "id"
            ) AS matched_skills ON matched_skills."id" = jobs."id"
                ORDER BY
                match_percentage,title DESC
      ) as subquery
      CROSS JOIN LATERAL jsonb_array_elements_text(subquery."skills") AS skill
      LEFT JOIN companies c on c."id" = subquery."companyId"
      LEFT JOIN locations l on l."id" = subquery."locationId"
      LEFT JOIN industries i on i."id" = subquery."industryId"
      LEFT JOIN subscribe s on s."jobId" = subquery."id"`;

    // Init 
    const conditions = [];

    // Title
    conditions.push(` subquery."isAssessment" = false AND subquery."status" ILIKE '%publish%'`)
    if (titleFilter) {
      conditions.push(` subquery."title" ILIKE '%${titleFilter}%'`);
    }

    // IndustryId
    if (industryId) {
      conditions.push(` subquery."industryId" = ${industryId}`);
    }

    // salaryMonthMin
    if (salaryMonthMin) {
      conditions.push(` subquery."salaryMonthMin" >= ${salaryMonthMin}`);
    }

    // salaryMonthMax
    if (salaryMonthMax) {
      conditions.push(` subquery."salaryMonthMax" <= ${salaryMonthMax}`);
    }

    // salaryYearMin
    if (salaryYearMin) {
      conditions.push(` subquery."salaryYearMin" >= ${salaryYearMin}`);
    }

    // salaryYearMax
    if (salaryYearMax) {
      conditions.push(` subquery."salaryYearMax" <= ${salaryYearMax}`);
    }

    // experienceMin
    if (experienceMin) {
      conditions.push(` subquery."experienceMin" >= ${experienceMin}`);
    }

    // experienceMax
    if (experienceMax) {
      conditions.push(` subquery."experienceMax" <= ${experienceMax}`);
    }

    // education
    if (education) {
      conditions.push(` subquery."education" = '${education}'`);
    }

    // companyId
    if (companyId) {
      conditions.push(` subquery."companyId" = '${companyId}'`);
    }

    // jobType
    if (jobType) {
      conditions.push(` subquery."jobType" = '${jobType}'`);
    }

    // preferableShift
    if (preferableShift) {
      conditions.push(` subquery."preferableShift" = '${preferableShift}'`);
    }

    // functionalArea
    if (functionalArea) {
      conditions.push(` subquery."functionalArea" = '${functionalArea}'`);
    }

    // Location
    if (locations) {
      const loc = [];
      if (Array.isArray(locations)) {
        locations.forEach(item => {
          loc.push(` subquery."locationId" = ${item}`)
        })
      } else {
        loc.push(` subquery."locationId" = ${locations}`)
      }
      conditions.push(` (${loc.join(' OR ')})`);
    }

    // Skills
    if (skills && skills.length) {
      if (Array.isArray(skills)) {
        conditions.push(` skill IN (${skills.map(item => `'${item}'`).join(', ')})`);
      } else {
        conditions.push(` skill IN ('${skills}')`);
      }
    }

    //posted On
    if (createdAtFrom) {
      conditions.push(` DATE(subquery."createdAt") >= '${createdAtFrom}'`)
    }

    if (createdTo) {
      conditions.push(` DATE(subquery."createdAt") <= '${createdTo}'`)
    }
    // Concat Conditional queries
    if (conditions.length > 0) {
      query += ` WHERE ${conditions.join(' AND ')} AND COALESCE(match_percentage,0)>0 `;
    }

    // Sortby & SortType
    if (dto.sortBy && dto.sortType) {
      query += ` ORDER BY match_percentage DESC,
            subquery."${dto.sortBy}" ${dto.sortType}`;
    } else {
      query += ` ORDER BY id, match_percentage, title DESC`;
    }
    // Execute the query
    const totalJobCount = await this.jobRepository.sequelize.query(query, {
      replacements: [...skillsCandidate],
      type: QueryTypes.SELECT
    });

    // Pagination
    if (limitValue) {
      query += ` LIMIT ${limitValue}`;

      if (offsetValue === 0 || offsetValue) {
        query += ` OFFSET ${offsetValue}`;
      }
    }

    // Execute the query
    const jobList = await this.jobRepository.sequelize.query(query, {
      replacements: [...skillsCandidate],
      type: QueryTypes.SELECT
    });

    return { count: totalJobCount.length, rows: jobList }
  }

  async countTodayMatchedJobs(userId: number) {
    const candidate = await this.candidateRepository.findOne({
      where: { userId: userId },
      include: [
        {
          model: Skill,
          attributes: ["name"],
        },
      ],
    });

    if (!candidate) {
      throw new HttpException('Candidate not found', HttpStatus.NOT_FOUND);
    }

    const skillsCandidate = candidate.skills.map(item => item.name);
    const locationId = candidate?.locationId;
    const remoteLocation = candidate?.remoteLocation;
    const positionId = candidate?.positionId;
    const educationCandidate = escapeString(candidate?.degree);
    const experience = candidate?.experience;
    const salary = candidate?.preferencesExpectedCTC;

    const dateStr = (new Date()).toISOString().split("T")[0]
    const data = await this.jobRepository.sequelize.query(`SELECT DISTINCT ON (subquery."id" ,subquery."match_percentage") subquery.*
      from
          (SELECT
              jobs."id",
              jobs."positionId",
              lj2."locationId" as "locationId",
              jobs."authorId",
              jobs."title",
              jobs."employer",
              jobs."consultancy",
              jobs."remoteLocation",
              jobs."salaryMonthMin",
              jobs."salaryMonthMax",
              jobs."salaryYearMin",
              jobs."salaryYearMax",
              jobs."negotiable",
              jobs."description",
              jobs."shortDescription",
              jobs."numberOpenings",
              jobs."jobType",
              jobs."preferableShift",
              jobs."industryId",
              jobs."functionalArea",
              jobs."noticePeriod",
              jobs."skills",
              jobs."experienceMin",
              jobs."experienceMax",
              jobs."education",
              jobs."careerPage",
              jobs."createdAt",
              jobs."companyId",
              jobs."status",
              jobs."isAssessment",
              COALESCE((
                  (   CASE WHEN matched_skills.matched_skill_count IS NULL THEN 0 ELSE matched_skills.matched_skill_count / jsonb_array_length(jobs.skills)::float * 30 END +  
                      CASE WHEN lj2."locationId" = ${locationId} THEN 10 ELSE 0 END +  -- Location points
                      CASE WHEN jobs."remoteLocation" = ${remoteLocation} THEN 5 ELSE 0 END +  -- Remote location points
                      CASE WHEN jobs."positionId" = ${positionId} THEN 20 ELSE 0 END +  -- Position points
                      CASE WHEN jobs."education" = '${educationCandidate}' THEN 10 ELSE 0 END +  -- Education points
                      CASE WHEN (jobs."experienceMin" >= ${experience} AND jobs."experienceMax" <= ${experience}) THEN 10 ELSE 0 END +  -- Experience points
                      CASE WHEN (jobs."salaryYearMin" >= ${salary} AND jobs."salaryYearMax" <= ${salary}) THEN 15 ELSE 0 END  -- Salary points
                  ) / 100.0 * 100), 0) AS match_percentage
          FROM
              jobs
          JOIN location_jobs AS lj2 ON lj2."jobId" = jobs."id"
          LEFT JOIN (
              SELECT
                  "id",
                  COUNT(DISTINCT skill) AS matched_skill_count
              FROM
                  jobs
              CROSS JOIN LATERAL jsonb_array_elements_text(jobs."skills") AS skill
              WHERE
                  skill IN (${skillsCandidate.map(() => '?').join(', ')})  -- Interpolating skills
              GROUP BY
                  "id"
          ) AS matched_skills ON matched_skills."id" = jobs."id"
              ORDER BY
              match_percentage,title DESC
    ) as subquery
    WHERE COALESCE(subquery."match_percentage",0) >0 and DATE(subquery."createdAt") = '${dateStr}' and subquery."isAssessment"=false`, {
      replacements: [...skillsCandidate],
      type: QueryTypes.SELECT
    });

    return { todayMatchedJobs: data.length }
  }

  async countMatchedJobs(dto: FilterJobDto, userId: number) {
    const candidate = await this.candidateRepository.findOne({
      where: { userId: userId },
      include: [
        {
          model: Skill,
          attributes: ["name"],
        },
      ],
    });

    if (!candidate) {
      throw new HttpException('Candidate not found', HttpStatus.NOT_FOUND);
    }

    const skillsCandidate = candidate.skills.map(item => item.name);
    const locationId = candidate?.locationId;
    const remoteLocation = candidate?.remoteLocation;
    const positionId = candidate?.positionId;
    const educationCandidate = escapeString(candidate?.degree);
    const experience = candidate?.experience;
    const salary = candidate?.preferencesExpectedCTC;

    const titleFilter = dto?.title;
    const industryId = dto?.industryId;
    const companyId = dto?.companyId;
    const jobType = dto?.jobType;
    const preferableShift = dto?.preferableShift;
    const functionalArea = dto?.functionalArea;

    const salaryMonthMin = dto?.salaryMonthMin;
    const salaryMonthMax = dto?.salaryMonthMax;
    const salaryYearMin = dto?.salaryYearMin;
    const salaryYearMax = dto?.salaryYearMax;
    const experienceMin = dto?.experienceMin;
    const experienceMax = dto?.experienceMax;

    const createdAtFrom = dto?.createdAtFrom;
    const createdTo = dto?.createdAtTo;

    const education = dto?.education;
    const skills = dto?.skills;

    const locations = dto?.locations;

    let query = `
        SELECT COUNT(DISTINCT subquery."id") as count
        from
            (SELECT
                jobs."id",
                jobs."positionId",
                lj2."locationId" as "locationId",
                jobs."authorId",
                jobs."title",
                jobs."employer",
                jobs."consultancy",
                jobs."remoteLocation",
                jobs."salaryMonthMin",
                jobs."salaryMonthMax",
                jobs."salaryYearMin",
                jobs."salaryYearMax",
                jobs."negotiable",
                jobs."description",
                jobs."shortDescription",
                jobs."numberOpenings",
                jobs."jobType",
                jobs."preferableShift",
                jobs."industryId",
                jobs."functionalArea",
                jobs."noticePeriod",
                jobs."skills",
                jobs."experienceMin",
                jobs."experienceMax",
                jobs."education",
                jobs."careerPage",
                jobs."createdAt",
                jobs."companyId",
                jobs."status",
                jobs."isAssessment",
                COALESCE((
                    (   CASE WHEN matched_skills.matched_skill_count IS NULL THEN 0 ELSE matched_skills.matched_skill_count / jsonb_array_length(jobs.skills)::float * 30 END +
                        CASE WHEN lj2."locationId" = ${locationId} THEN 10 ELSE 0 END +  -- Location points
                        CASE WHEN jobs."remoteLocation" = ${remoteLocation} THEN 5 ELSE 0 END +  -- Remote location points
                        CASE WHEN jobs."positionId" = ${positionId} THEN 20 ELSE 0 END +  -- Position points
                        CASE WHEN jobs."education" = '${educationCandidate}' THEN 10 ELSE 0 END +  -- Education points
                        CASE WHEN (jobs."experienceMin" >= ${experience} AND jobs."experienceMax" <= ${experience}) THEN 10 ELSE 0 END +  -- Experience points
                        CASE WHEN (jobs."salaryYearMin" >= ${salary} AND jobs."salaryYearMax" <= ${salary}) THEN 15 ELSE 0 END  -- Salary points
                    ) / 100.0 * 100), 0) AS match_percentage
            FROM
                jobs
            JOIN location_jobs AS lj2 ON lj2."jobId" = jobs."id"
            LEFT JOIN (
                SELECT
                    "id",
                    COUNT(DISTINCT skill) AS matched_skill_count
                FROM
                    jobs
                CROSS JOIN LATERAL jsonb_array_elements_text(jobs."skills") AS skill
                WHERE
                    skill IN (${skillsCandidate.map(() => '?').join(', ')})  -- Interpolating skills
                GROUP BY
                    "id"
            ) AS matched_skills ON matched_skills."id" = jobs."id"
                ORDER BY
                match_percentage,title DESC
      ) as subquery
      CROSS JOIN LATERAL jsonb_array_elements_text(subquery."skills") AS skill`;

    // Init
    const conditions = [];

    // Title
    conditions.push(` subquery."isAssessment" = false AND subquery."status" ILIKE '%publish%'`)
    if (titleFilter) {
      conditions.push(` subquery."title" ILIKE '%${titleFilter}%'`);
    }

    // IndustryId
    if (industryId) {
      conditions.push(` subquery."industryId" = ${industryId}`);
    }

    // salaryMonthMin
    if (salaryMonthMin) {
      conditions.push(` subquery."salaryMonthMin" >= ${salaryMonthMin}`);
    }

    // salaryMonthMax
    if (salaryMonthMax) {
      conditions.push(` subquery."salaryMonthMax" <= ${salaryMonthMax}`);
    }

    // salaryYearMin
    if (salaryYearMin) {
      conditions.push(` subquery."salaryYearMin" >= ${salaryYearMin}`);
    }

    // salaryYearMax
    if (salaryYearMax) {
      conditions.push(` subquery."salaryYearMax" <= ${salaryYearMax}`);
    }

    // experienceMin
    if (experienceMin) {
      conditions.push(` subquery."experienceMin" >= ${experienceMin}`);
    }

    // experienceMax
    if (experienceMax) {
      conditions.push(` subquery."experienceMax" <= ${experienceMax}`);
    }

    // education
    if (education) {
      conditions.push(` subquery."education" = '${education}'`);
    }

    // companyId
    if (companyId) {
      conditions.push(` subquery."companyId" = '${companyId}'`);
    }

    // jobType
    if (jobType) {
      conditions.push(` subquery."jobType" = '${jobType}'`);
    }

    // preferableShift
    if (preferableShift) {
      conditions.push(` subquery."preferableShift" = '${preferableShift}'`);
    }

    // functionalArea
    if (functionalArea) {
      conditions.push(` subquery."functionalArea" = '${functionalArea}'`);
    }

    // Location
    if (locations) {
      const loc = [];
      if (Array.isArray(locations)) {
        locations.forEach(item => {
          loc.push(` subquery."locationId" = ${item}`)
        })
      } else {
        loc.push(` subquery."locationId" = ${locations}`)
      }
      conditions.push(` (${loc.join(' OR ')})`);
    }

    // Skills
    if (skills && skills.length) {
      if (Array.isArray(skills)) {
        conditions.push(` skill IN (${skills.map(item => `'${item}'`).join(', ')})`);
      } else {
        conditions.push(` skill IN ('${skills}')`);
      }
    }

    //posted On
    if (createdAtFrom) {
      conditions.push(` DATE(subquery."createdAt") >= '${createdAtFrom}'`)
    }

    if (createdTo) {
      conditions.push(` DATE(subquery."createdAt") <= '${createdTo}'`)
    }
    // Concat Conditional queries
    if (conditions.length > 0) {
      query += ` WHERE ${conditions.join(' AND ')} AND COALESCE(match_percentage,0)>0 `;
    }

    // Execute the query
    const result = await this.jobRepository.sequelize.query(query, {
      replacements: [...skillsCandidate],
      type: QueryTypes.SELECT
    });

    return { matchedJobsCount: result[0]['count'] }
  }

  async createAssignmentForAssessment(dto, userId, companyId, authHeader = '') {
    //create workflow
    dto.deadline = (dto.deadline.split(" ")[0] / 24).toFixed(1)
    const workflow = [workflowType[dto.assessmentType]]
    const deadlineType = dto.assessmentType === 'take-home'
      ? { takeHomeTaskDeadline: dto.deadline, takeHomeTaskId: dto.takeHomeTaskId, takeHomeTaskTime: dto.duration }
      : dto.assessmentType === 'live-task'
        ? { liveCodingId: dto.liveCodingId, liveCodingTime: dto.duration }
        : { domainDeadline: dto.deadline, domainId: dto.domainId }
    const workflowData = { workflow, title: dto.name, domainReviewer: dto.reviewers, ...deadlineType, }

    const workflowD = await this.workflowService.create(workflowData, userId, companyId)

    //create dynamic job with the created workflow id
    if (workflowD) {
      const job = { ...dummyJobData }
      job.title = dto.name;
      job.workflowId = workflowD.id;
      job.isAssessment = true;
      return await this.createJob(job, companyId, userId, authHeader)
    } else {
      throw new InternalServerErrorException("Failed to create assignment.")
    }
  }

  getJobsRecordData(job: Jobs, action) {
    if (!job) {
      return
    }
    return {
      for: 'job',
      data: {
        companyId: job.companyId,
        userId: job.authorId,
        jobId: job.id,
        eventType: action,
        eventDateTime: new Date(),
        "job_posted": {
          aboutCompany: job.aboutCompany,
          authorId: job.authorId,
          benefits: job.benefits || [],
          careerPage: job.careerPage,
          careerPortal: job.careerPortal,
          company: { id: job.companyId, name: job.company?.name, tenantId: job.company?.tenantId },
          companyId: job.companyId,
          consultancy: job.consultancy,
          createdAt: job.createdAt,
          description: job.description,
          education: job.education,
          employer: job.employer,
          experienceMax: job.experienceMax,
          experienceMin: job.experienceMin,
          functionalArea: job.functionalArea,
          facebook: job.facebook,
          id: job.id,
          industry: job.industry,
          industryId: job.industryId,
          instagram: job.instagram,
          jobBoards: job.jobBoards,
          jobType: job.jobType,
          linkedin: job.linkedin,
          locations: job.locations,
          negotiable: job.negotiable,
          noticePeriod: job.noticePeriod,
          numberOpenings: job.numberOpenings,
          positionId: job.positionId,
          preferableShift: job.preferableShift,
          publicSearch: job.publicSearch,
          remoteLocation: job.remoteLocation,
          salaryMonthMax: job.salaryMonthMax,
          salaryMonthMin: job.salaryMonthMin,
          salaryYearMax: job.salaryYearMax,
          salaryYearMin: job.salaryYearMin,
          salaryHourMax: job.salaryHourMax,
          salaryHourMin: job.salaryHourMin,
          shortDescription: job.shortDescription,
          skills: job.skills || [],
          status: job.status,
          title: job.title,
          twitter: job.twitter,
          updatedAt: job.updatedAt,
          workflow: job.workflow,
          workflowId: job.workflowId,
          approverId: job.approverId,
        },
      }
    }
  }
  getJobDataForQdrant(job: Jobs, action = 'create') {
    if (!job || job?.isAssessment) {
      return
    }
    const strParts = action === 'create' ? [
      job.title && `JobTitle: ${job.title}`,
      job.employer && `Employer: ${job.employer}`,
      job.companyId && `Company Id: ${job.companyId}`,
      job.jobType && `Job Type: ${job.jobType}`,
      job.status && `Job Status: ${job.status}`,
      job.locations?.length > 0 && `Job Location: ${job.locations.map(location => `${location.id}:${location.city}, ${location.state}`).join(',')}`,
      job.remoteLocation !== null && `Job Remote Location: ${job.remoteLocation}`,
      (job.salaryMonthMin !== null && job.salaryMonthMax !== null) &&
      `Job Salary Month Range: ${job.salaryMonthMin} - ${job.salaryMonthMax}`,
      (job.salaryYearMin !== null && job.salaryYearMax !== null) &&
      `Job Salary Year Range: ${job.salaryYearMin} - ${job.salaryYearMax}`,
      (job.salaryHourMin !== null && job.salaryHourMax !== null) &&
      `Job Salary Hour Range: ${job.salaryHourMin} - ${job.salaryHourMax}`,
      job.negotiable !== null && `Job Negotiable: ${job.negotiable}`,
      job.numberOpenings && `Job Number Openings: ${job.numberOpenings}`,
      job.description && `Job Description: ${job.description}`,
      job.shortDescription && `Job Short Description: ${job.shortDescription}`,
      job.skills?.length > 0 && `Job Skills: ${job.skills.join(',')}`,
      (job.experienceMin !== null && job.experienceMax !== null) &&
      `Job Experience Range: ${job.experienceMin} - ${job.experienceMax}`,
      job.education && `Job Education: ${job.education}`,
      job.benefits?.length > 0 && `Job Benefits: ${job.benefits.join(',')}`,
      job.aboutCompany && `Job About Company: ${job.aboutCompany}`,
      job.linkedin && `LinkedIn Profile: ${job.linkedin}`,
      job.facebook && `Facebook Profile: ${job.facebook}`,
      job.twitter && `Twitter Profile: ${job.twitter}`,
      job.instagram && `Instagram Profile: ${job.instagram}`,
      job.careerPortal && `Career Page: ${job.careerPortal}`
    ].filter(Boolean).join('\n') : '';

    return {
      for: 'jobs',
      id: job.id,
      action,
      ...(action === 'create' && {
        str: strParts,
        payload: {
          title: job.title,
          id: job.id,
          workflowId: job.workflowId,
          companyId: job.companyId,
          jobType: job.jobType,
          status: job.status,
          location: job.locations,
          remoteLocation: job.remoteLocation,
          salaryMonthMin: job.salaryMonthMin,
          salaryMonthMax: job.salaryMonthMax,
          salaryYearMin: job.salaryYearMin,
          salaryYearMax: job.salaryYearMax,
          salaryHourMin: job.salaryHourMin,
          salaryHourMax: job.salaryHourMax,
          negotiable: job.negotiable,
          functionalArea: job.functionalArea,
          noticePeriod: job.noticePeriod,
          numberOpenings: job.numberOpenings,
          description: job.description,
          shortDescription: job.shortDescription,
          skills: job.skills,
          employer: job.employer,
          aboutCompany: job.aboutCompany,
          linkedin: job.linkedin,
          facebook: job.facebook,
          twitter: job.twitter,
          instagram: job.instagram,
          careerPortal: job.careerPortal,
          benefits: job.benefits,
          position: job.position,
          industry: job.industry,
          experienceMin: job.experienceMin,
          experienceMax: job.experienceMax,
          authorId: job.authorId,
          approvalId: job.approverId,
          education: job.education,
          createdAt: job.createdAt,
        }
      })
    }
  }
}

async function createAssignment(jobCreated: Jobs, job: Jobs, httpService, token) {
  if (!jobCreated?.workflowId) {
    return;
  }
  const headers = {
    Authorization: token,
  };
  const assignmentEndpoint = `${process.env.ASSESSMENT_API_URI}/api/assignment/job`;
  const isAvailable = await httpService?.get(`${assignmentEndpoint}/${job?.id}`, { headers }).toPromise();
  const existingWorkflowId = isAvailable?.data?.workflowId;


  if (!isAvailable || (isAvailable && existingWorkflowId !== null)) {
    let deadline: any = {};

    if (jobCreated.workflow?.takeHomeTaskDeadline !== null) {
      deadline = { takeHomeTaskDeadline: jobCreated.workflow.takeHomeTaskDeadline };
    }

    if (jobCreated.workflow?.domainDeadline !== null) {
      deadline.domainDeadline = jobCreated.workflow.domainDeadline;
    }

    const obj = {
      title: jobCreated.title,
      jobId: job.id,
      workflowId: jobCreated.workflowId,
      domainId: jobCreated.workflow?.domainId || null,
      takeHomeTaskId: jobCreated.workflow.takeHomeTaskId || null,
      liveCodingId: jobCreated.workflow.liveCodingId || null,
      ...deadline,
      companyId: jobCreated.companyId,
      reviewers: jobCreated.workflow?.domainReviewer || [],
    };

    try {
      if (isAvailable?.data !== null && isAvailable?.data?.isActive === true) {
        await httpService.patch(`${assignmentEndpoint}/${isAvailable.data.id}`, obj, { headers }).toPromise();
      } else {
        await httpService.post(assignmentEndpoint, obj, { headers }).toPromise();
      }
    } catch (err) {

      Logger.log("Error while creating assignment data for a job:", err);
    }
  }
}

const dummyJobData = {
  "title": "Angular Developer",
  "employer": "Recruits",
  "approvalEmail": "",
  "approvalName": "",
  "consultancy": "Consultancy",
  "locations": [
    {
      "id": 1,
      "value": "location",
      "label": "Location"
    }
  ],
  "location": "false",
  "remoteLocation": false,
  "salaryMonthMin": 500,
  "salaryMonthMax": 500,
  "salaryYearMin": 6000,
  "salaryYearMax": 6000,
  "salaryHourMin": 60,
  "salaryHourMax": 150,
  "negotiable": false,
  "description": "TEXT",
  "shortDescription": "TEXT",
  "numberOpenings": "1",
  "jobType": "Full Time",
  "preferableShift": "Morning Shift",
  "positionId": 1,
  "industryId": 1,
  "functionalArea": "Functional Area",
  "noticePeriod": "2 Months",
  "skills": [
    "Communication Skills"
  ],
  "experienceMin": 1,
  "experienceMax": 5,
  "education": "Master Degree",
  "screeningQuestions": [],
  "benefits": [],
  "aboutCompany": "TEXT",
  "workflowId": 1,
  "careerPortal": "Enter employer",
  "linkedin": "url",
  "facebook": "url",
  "twitter": "url",
  "instagram": "url",
  "careerPage": false,
  "publicSearch": false,
  "jobBoards": [],
  "applicationForm": "Json",
  "status": "publish",
  "isAssessment": false
}

function escapeString(value) {
  if (value) {
    return value.replace(/'/g, "''");
  }
  return '';
}

