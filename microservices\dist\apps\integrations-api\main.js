/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./apps/integrations-api/src/app.module.ts":
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AppModule = void 0;
const tslib_1 = __webpack_require__("tslib");
const common_1 = __webpack_require__("@nestjs/common");
const microservices_1 = __webpack_require__("@nestjs/microservices");
const healthcheck_module_1 = __webpack_require__("./apps/integrations-api/src/healthcheck/healthcheck.module.ts");
const google_module_1 = __webpack_require__("./apps/integrations-api/src/google/google.module.ts");
const facebook_module_1 = __webpack_require__("./apps/integrations-api/src/facebook/facebook.module.ts");
const email_1 = __webpack_require__("./libs/email/src/index.ts");
const axios_1 = __webpack_require__("@nestjs/axios");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = tslib_1.__decorate([
    (0, common_1.Module)({
        imports: [
            microservices_1.ClientsModule.register([
                {
                    name: 'INTEGRATIONS_SERVICE',
                    transport: microservices_1.Transport.REDIS,
                    options: {
                        url: process.env.REDIS_URL || 'redis://localhost:6379',
                    },
                },
            ]),
            healthcheck_module_1.HealthcheckModule,
            google_module_1.GoogleModule,
            facebook_module_1.FacebookModule,
            email_1.EmailModule,
            axios_1.HttpModule
        ],
        providers: [
            email_1.EmailService,
        ],
    })
], AppModule);


/***/ }),

/***/ "./apps/integrations-api/src/facebook/facebook.controller.ts":
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.FacebookController = void 0;
const tslib_1 = __webpack_require__("tslib");
const common_1 = __webpack_require__("@nestjs/common");
const swagger_1 = __webpack_require__("@nestjs/swagger");
const facebook_service_1 = __webpack_require__("./apps/integrations-api/src/facebook/facebook.service.ts");
// TODO: this controller should be removed after the job is finished
let FacebookController = class FacebookController {
    constructor(facebookService) {
        this.facebookService = facebookService;
    }
    listJobs() {
        return this.facebookService.jobsXml();
    }
};
exports.FacebookController = FacebookController;
tslib_1.__decorate([
    (0, swagger_1.ApiOperation)({
        summary: 'List of jobs in XML format for facebook.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Returns an XML document.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Returns an Error object.',
    }),
    (0, common_1.Get)('/jobs.xml'),
    (0, common_1.Header)('Content-Type', 'text/xml'),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", []),
    tslib_1.__metadata("design:returntype", void 0)
], FacebookController.prototype, "listJobs", null);
exports.FacebookController = FacebookController = tslib_1.__decorate([
    (0, swagger_1.ApiTags)('Facebook'),
    (0, common_1.Controller)('facebook'),
    tslib_1.__metadata("design:paramtypes", [typeof (_a = typeof facebook_service_1.FacebookService !== "undefined" && facebook_service_1.FacebookService) === "function" ? _a : Object])
], FacebookController);


/***/ }),

/***/ "./apps/integrations-api/src/facebook/facebook.module.ts":
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.FacebookModule = void 0;
const tslib_1 = __webpack_require__("tslib");
const common_1 = __webpack_require__("@nestjs/common");
const facebook_service_1 = __webpack_require__("./apps/integrations-api/src/facebook/facebook.service.ts");
const facebook_controller_1 = __webpack_require__("./apps/integrations-api/src/facebook/facebook.controller.ts");
let FacebookModule = class FacebookModule {
};
exports.FacebookModule = FacebookModule;
exports.FacebookModule = FacebookModule = tslib_1.__decorate([
    (0, common_1.Module)({
        providers: [facebook_service_1.FacebookService],
        controllers: [facebook_controller_1.FacebookController],
        exports: [facebook_service_1.FacebookService],
    })
], FacebookModule);


/***/ }),

/***/ "./apps/integrations-api/src/facebook/facebook.service.ts":
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.FacebookService = void 0;
const tslib_1 = __webpack_require__("tslib");
const common_1 = __webpack_require__("@nestjs/common");
let FacebookService = class FacebookService {
    /**
     * Retrieve a Jobs XML.
     *
     * @throws Error
     *
     * @returns {string}
     */
    jobsXml() {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            let xml = '<?xml version="1.0" encoding="UTF-8"?>';
            xml += '<source>';
            xml += '<publisher-name><![CDATA[uRecruits]]></publisher-name>';
            xml += '<publisher-url><![CDATA[https://urecruits.com/]]></publisher-url>';
            xml += `<last-build-date><![CDATA[${new Date().toISOString().slice(0, 19).replace('T', ' ')} +0000]]></last-build-date>`;
            /* job item start */
            xml += '<job>';
            xml += '<!-- Basic job info -->';
            xml += '<title><![CDATA[Cashier]]></title>';
            xml += '<date><![CDATA[2022-09-30 09:31:48 +0000]]></date>';
            xml += '<id><![CDATA[job7]]></id>';
            xml += '<photo-url><![CDATA[https://urecruits-dev-pre-recruitment-api-uploads-public.s3.amazonaws.com/company/auth0%7C6334255cab2d886bb3b55e1f/1664440463882-photo_2022-08-19_09-52-49.jpg]]></photo-url>';
            xml += '<job-type>FULL_TIME</job-type>';
            xml += '<description><![CDATA[Job description]]></description>';
            xml += '<!-- Company info -->';
            xml += '<company-name><![CDATA[AutoMobile Technologies (AMT)]]></company-name>';
            xml += '<company-id><![CDATA[company3]]></company-id>';
            xml += '<company-full-address><![CDATA[1 Hacker Way, Menlo Park, CA, 94025]]></company-full-address>';
            xml += '<company-facebook-url><![CDATA[https://www.facebook.com/Jaspers-Market-909582695755501/]]></company-facebook-url>';
            xml += '<company-data-policy-url><![CDATA[https://www.jaspers-market.com/data-policy.php]]></company-data-policy-url>';
            xml += '<company-url><![CDATA[https://www.ibm.com/]]></company-url>';
            xml += '<company-page-matching-approach>STANDARD</company-page-matching-approach>';
            xml += '<!-- Location of Job -->';
            xml += '<full-address><![CDATA[1 Hacker Way, Menlo Park, CA, 94025]]></full-address>';
            xml += '<house-number><![CDATA[1]]></house-number>';
            xml += '<street-name><![CDATA[Hacker Way]]></street-name>';
            xml += '<city><![CDATA[Menlo Park]]></city>';
            xml += '<region><![CDATA[CA]]></region>';
            xml += '<country><![CDATA[US]]></country>';
            xml += '<postal-code><![CDATA[94025]]></postal-code>';
            xml += '<!-- Salary -->';
            xml += '<salary><![CDATA[55.50]]></salary>';
            xml += '<salary-currency>USD</salary-currency>';
            xml += '<salary-type>HOURLY</salary-type>';
            xml += '<!-- Integration configuration -->';
            xml += '<facebook-apply-data>';
            xml += '  <application-callback-url><![CDATA[https://urecruits.com/callback?job=unique11111]]></application-callback-url>';
            xml += '  <custom-questions-url><![CDATA[https://urecruits.com/custom-questions?job=unique11111]]></custom-questions-url>';
            xml += '  <form-config>';
            xml += '    <email-field>';
            xml += '      <optional>TRUE</optional>';
            xml += '    </email-field>';
            xml += '    <phone-number-field>';
            xml += '      <optional>FALSE</optional>';
            xml += '    </phone-number-field>';
            xml += '    <work-experience-field>';
            xml += '      <optional>TRUE</optional>';
            xml += '    </work-experience-field>';
            xml += '  </form-config>';
            xml += '</facebook-apply-data>';
            xml += '</job>';
            /* job item end */
            xml += '</source>';
            return xml;
        });
    }
};
exports.FacebookService = FacebookService;
exports.FacebookService = FacebookService = tslib_1.__decorate([
    (0, common_1.Injectable)()
], FacebookService);


/***/ }),

/***/ "./apps/integrations-api/src/google/dto/job.dto.ts":
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.JobDto = void 0;
const tslib_1 = __webpack_require__("tslib");
const swagger_1 = __webpack_require__("@nestjs/swagger");
class JobDto {
}
exports.JobDto = JobDto;
tslib_1.__decorate([
    (0, swagger_1.ApiProperty)({
        example: '123abc',
        description: 'Job requisition ID, aka Posting ID. Unique per job. Can be ID from uRecruits database. The maximum number of allowed characters is 225.',
        required: true,
    }),
    tslib_1.__metadata("design:type", String)
], JobDto.prototype, "requisitionId", void 0);
tslib_1.__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'Software Engineer.',
        description: 'The job title. The maximum number of allowed characters is 500.',
        required: true,
    }),
    tslib_1.__metadata("design:type", String)
], JobDto.prototype, "title", void 0);
tslib_1.__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'The description of the job.',
        description: 'This field accepts and sanitizes HTML input, and accepts bold, italic, ordered list, and unordered list markup tags. The maximum number of allowed characters is 100,000.',
        required: true,
    }),
    tslib_1.__metadata("design:type", String)
], JobDto.prototype, "description", void 0);
tslib_1.__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'https://www.example.org/job-posting/123',
        description: 'Use this URI field to direct an applicant to a website, for example to link to an online application form. The maximum number of allowed characters for each entry is 2,000.',
        required: false,
    }),
    tslib_1.__metadata("design:type", String)
], JobDto.prototype, "jobApplicationUrl", void 0);
tslib_1.__decorate([
    (0, swagger_1.ApiProperty)({
        example: '1600 Amphitheatre Parkway, Mountain View, CA 94043',
        description: 'Job location. The maximum number of allowed characters is 500.',
        required: false,
    }),
    tslib_1.__metadata("design:type", String)
], JobDto.prototype, "address", void 0);
tslib_1.__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'en-US',
        description: 'Language code.',
        required: false,
    }),
    tslib_1.__metadata("design:type", String)
], JobDto.prototype, "languageCode", void 0);


/***/ }),

/***/ "./apps/integrations-api/src/google/google.controller.ts":
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


var _a, _b, _c;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.GoogleController = void 0;
const tslib_1 = __webpack_require__("tslib");
const common_1 = __webpack_require__("@nestjs/common");
const swagger_1 = __webpack_require__("@nestjs/swagger");
const google_service_1 = __webpack_require__("./apps/integrations-api/src/google/google.service.ts");
const job_dto_1 = __webpack_require__("./apps/integrations-api/src/google/dto/job.dto.ts");
// TODO: this controller should be removed after the job is finished
let GoogleController = class GoogleController {
    constructor(googleService) {
        this.googleService = googleService;
    }
    createJob(dto) {
        return this.googleService.createJob(dto);
    }
    updateJob(jobId, dto) {
        return this.googleService.updateJob(jobId, dto);
    }
    deleteJob(jobId) {
        return this.googleService.deleteJob(jobId);
    }
    listJobs(requisitionId) {
        return this.googleService.listJobs(requisitionId);
    }
    retrieveJob(jobId) {
        return this.googleService.retrieveJob(jobId);
    }
};
exports.GoogleController = GoogleController;
tslib_1.__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Create a Job.' }),
    (0, swagger_1.ApiResponse)({
        status: 201, description: 'Returns a Job object.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Returns an Error object.',
    }),
    (0, common_1.Post)('/create-job'),
    tslib_1.__param(0, (0, common_1.Body)()),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [typeof (_b = typeof job_dto_1.JobDto !== "undefined" && job_dto_1.JobDto) === "function" ? _b : Object]),
    tslib_1.__metadata("design:returntype", void 0)
], GoogleController.prototype, "createJob", null);
tslib_1.__decorate([
    (0, swagger_1.ApiOperation)({
        summary: 'Update a Job.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Returns a Job object.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Returns an Error object.',
    }),
    (0, common_1.Put)('/update-job/:jobId'),
    tslib_1.__param(0, (0, common_1.Param)('jobId')),
    tslib_1.__param(1, (0, common_1.Body)()),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String, typeof (_c = typeof job_dto_1.JobDto !== "undefined" && job_dto_1.JobDto) === "function" ? _c : Object]),
    tslib_1.__metadata("design:returntype", void 0)
], GoogleController.prototype, "updateJob", null);
tslib_1.__decorate([
    (0, swagger_1.ApiOperation)({
        summary: 'Delete a Job.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Returns a Job object.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Returns an Error object.',
    }),
    (0, common_1.Delete)('/delete-job/:jobId'),
    tslib_1.__param(0, (0, common_1.Param)('jobId')),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String]),
    tslib_1.__metadata("design:returntype", void 0)
], GoogleController.prototype, "deleteJob", null);
tslib_1.__decorate([
    (0, swagger_1.ApiOperation)({
        summary: 'List all jobs. If present optional parameter "requisitionId" result will be filtered by that value.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Returns an Array of Job objects.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Returns an Error object.',
    }),
    (0, common_1.Get)('/list-jobs/:requisitionId?'),
    tslib_1.__param(0, (0, common_1.Param)('requisitionId')),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String]),
    tslib_1.__metadata("design:returntype", void 0)
], GoogleController.prototype, "listJobs", null);
tslib_1.__decorate([
    (0, swagger_1.ApiOperation)({
        summary: 'Retrieve a Job by ID (Google side ID).',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Returns a Job object.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Returns an Error object.',
    }),
    (0, common_1.Get)('/retrieve-job/:jobId'),
    tslib_1.__param(0, (0, common_1.Param)('jobId')),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", [String]),
    tslib_1.__metadata("design:returntype", void 0)
], GoogleController.prototype, "retrieveJob", null);
exports.GoogleController = GoogleController = tslib_1.__decorate([
    (0, swagger_1.ApiTags)('Google'),
    (0, common_1.Controller)('google'),
    tslib_1.__metadata("design:paramtypes", [typeof (_a = typeof google_service_1.GoogleService !== "undefined" && google_service_1.GoogleService) === "function" ? _a : Object])
], GoogleController);


/***/ }),

/***/ "./apps/integrations-api/src/google/google.module.ts":
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.GoogleModule = void 0;
const tslib_1 = __webpack_require__("tslib");
const common_1 = __webpack_require__("@nestjs/common");
const google_service_1 = __webpack_require__("./apps/integrations-api/src/google/google.service.ts");
const google_controller_1 = __webpack_require__("./apps/integrations-api/src/google/google.controller.ts");
let GoogleModule = class GoogleModule {
};
exports.GoogleModule = GoogleModule;
exports.GoogleModule = GoogleModule = tslib_1.__decorate([
    (0, common_1.Module)({
        providers: [google_service_1.GoogleService],
        controllers: [google_controller_1.GoogleController],
        exports: [google_service_1.GoogleService],
    })
], GoogleModule);


/***/ }),

/***/ "./apps/integrations-api/src/google/google.service.ts":
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.GoogleService = void 0;
const tslib_1 = __webpack_require__("tslib");
const common_1 = __webpack_require__("@nestjs/common");
const talent = __webpack_require__("@google-cloud/talent");
/**
 * Job Search hierarchy:
 * 1. Tenant -  uRecruits.
 *              Tenant can include many Companies.
 * 2. Company - for now uRecruits only, in the future can be any company registered in uRecruits database.
 *              Each company can include many Jobs.
 * 3. Job
 */
let GoogleService = class GoogleService {
    constructor() {
        this.keyFilename = './urecruits-2021-67e4158d62b6.json';
        this.projectId = 'urecruits-2021';
        this.defaultTenantId = '2cc9ad17-3f00-0000-0000-00fce8b59334';
        this.defaultCompanyId = '53d2f60e-82dc-4b86-a0d5-d4c2c703a3bb';
    }
    /**
     * Create a job.
     *
     * @param dto {JobDto}
     *
     * @throws Error
     *
     * @returns {object}
     */
    createJob(dto) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            const client = new talent.JobServiceClient({
                keyFilename: this.keyFilename,
            });
            const formattedParent = client.projectPath(this.projectId);
            const job = {
                company: this.defaultCompanyId,
                requisitionId: dto.requisitionId,
                title: dto.title,
                description: dto.description,
            };
            if (dto.jobApplicationUrl) {
                job['applicationInfo'] = {
                    uris: [dto.jobApplicationUrl],
                };
            }
            if (dto.address) {
                job['addresses'] = [dto.address];
            }
            if (dto.languageCode) {
                job['languageCode'] = dto.languageCode;
            }
            const request = {
                parent: formattedParent,
                job,
            };
            const response = yield client.createJob(request).catch((err) => {
                throw new common_1.HttpException(err, common_1.HttpStatus.BAD_REQUEST);
            });
            return response[0];
        });
    }
    /**
     *
     * @param jobId
     * @param dto
     *
     */
    updateJob(jobId, dto) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            const client = new talent.JobServiceClient({
                keyFilename: this.keyFilename,
            });
            const formattedName = client.jobPath(this.projectId, this.defaultTenantId, jobId);
            const job = {
                name: formattedName,
                company: this.defaultCompanyId,
                requisitionId: dto.requisitionId,
                title: dto.title,
                description: dto.description,
            };
            if (dto.jobApplicationUrl) {
                job['applicationInfo'] = {
                    uris: [dto.jobApplicationUrl],
                };
            }
            if (dto.address) {
                job['addresses'] = [dto.address];
            }
            if (dto.languageCode) {
                job['languageCode'] = dto.languageCode;
            }
            const request = {
                job,
            };
            const response = yield client.updateJob(request).catch((err) => {
                throw new common_1.HttpException(err, common_1.HttpStatus.BAD_REQUEST);
            });
            return response[0];
        });
    }
    /**
     * Delete a Job.
     *
     * @param jobId {string} Job ID
     *
     * @throws Error
     *
     * @returns {object}
     */
    deleteJob(jobId) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            const client = new talent.JobServiceClient({
                keyFilename: this.keyFilename,
            });
            const formattedName = client.jobPath(this.projectId, this.defaultTenantId, jobId);
            const request = {
                name: formattedName,
            };
            const response = yield client.deleteJob(request).catch((err) => {
                throw new common_1.HttpException(err, common_1.HttpStatus.BAD_REQUEST);
            });
            return response[0];
        });
    }
    /**
     * List Jobs.
     *
     * @param requisitionId {string}
     *
     * @throws Error
     *
     * @returns {array}
     */
    listJobs(requisitionId) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            const client = new talent.JobServiceClient({
                keyFilename: this.keyFilename,
            });
            const formattedParent = client.projectPath(this.projectId);
            let filter = `companyName = "projects/${this.projectId}/companies/${this.defaultCompanyId}"`;
            if (requisitionId) {
                filter += ` AND requisitionId ="${requisitionId}"`;
            }
            const request = {
                parent: formattedParent,
                filter,
            };
            const response = yield client.listJobs(request).catch((err) => {
                throw new common_1.HttpException(err, common_1.HttpStatus.BAD_REQUEST);
            });
            return response[0];
        });
    }
    /**
     * Retrieve a Job.
     *
     * @param jobId {string} Job ID
     *
     * @throws Error
     *
     * @returns {object}
     */
    retrieveJob(jobId) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            const client = new talent.JobServiceClient({
                keyFilename: this.keyFilename,
            });
            const formattedName = client.jobPath(this.projectId, this.defaultTenantId, jobId);
            const request = {
                name: formattedName,
            };
            const response = yield client.getJob(request).catch((err) => {
                throw new common_1.HttpException(err, common_1.HttpStatus.BAD_REQUEST);
            });
            return response[0];
        });
    }
    /**
     * List Tenants.
     *
     * @throws Error
     *
     * @returns {array}
     */
    listTenants() {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            const client = new talent.TenantServiceClient({
                keyFilename: this.keyFilename,
            });
            const formattedParent = client.projectPath(this.projectId);
            const request = {
                parent: formattedParent,
            };
            const response = yield client.listTenants(request).catch((err) => {
                throw new common_1.HttpException(err, common_1.HttpStatus.BAD_REQUEST);
            });
            return response[0];
        });
    }
    /**
     * Create a Company.
     * For now only uRecruits, in the future it can be any company registered in the uRecruits database.
     * Each company can include many Jobs.
     *
     * @param displayName {string} Company Name
     * @param externalId {string} Identifier of this company on uRecruits side
     *
     * @throws Error
     *
     * @returns {object}
     */
    createCompany(displayName, externalId) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            const client = new talent.CompanyServiceClient({
                keyFilename: this.keyFilename,
            });
            const formattedParent = client.tenantPath(this.projectId, this.defaultTenantId);
            const company = {
                displayName,
                externalId,
            };
            const request = {
                parent: formattedParent,
                company,
            };
            const response = yield client.createCompany(request).catch((err) => {
                throw new common_1.HttpException(err, common_1.HttpStatus.BAD_REQUEST);
            });
            return response[0];
        });
    }
    /**
     * List Companies.
     *
     * @throws Error
     *
     * @returns {array}
     */
    listCompanies() {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            const client = new talent.CompanyServiceClient({
                keyFilename: this.keyFilename,
            });
            const formattedParent = client.tenantPath(this.projectId, this.defaultTenantId);
            const request = {
                parent: formattedParent,
            };
            const response = yield client.listCompanies(request).catch((err) => {
                throw new common_1.HttpException(err, common_1.HttpStatus.BAD_REQUEST);
            });
            return response[0];
        });
    }
    /**
     * Delete a Company.
     *
     * @param companyId {string} Company ID
     *
     * @throws Error
     *
     * @returns {object}
     */
    deleteCompany(companyId) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            const client = new talent.CompanyServiceClient({
                keyFilename: this.keyFilename,
            });
            const formattedName = client.companyPath(this.projectId, this.defaultTenantId, companyId);
            const request = {
                name: formattedName,
            };
            const response = yield client.deleteCompany(request).catch((err) => {
                throw new common_1.HttpException(err, common_1.HttpStatus.BAD_REQUEST);
            });
            return response[0];
        });
    }
};
exports.GoogleService = GoogleService;
exports.GoogleService = GoogleService = tslib_1.__decorate([
    (0, common_1.Injectable)()
], GoogleService);


/***/ }),

/***/ "./apps/integrations-api/src/healthcheck/healthcheck.controller.ts":
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.HealthcheckController = void 0;
const tslib_1 = __webpack_require__("tslib");
const common_1 = __webpack_require__("@nestjs/common");
const swagger_1 = __webpack_require__("@nestjs/swagger");
const healthcheck_service_1 = __webpack_require__("./apps/integrations-api/src/healthcheck/healthcheck.service.ts");
let HealthcheckController = class HealthcheckController {
    constructor(healthcheckService) {
        this.healthcheckService = healthcheckService;
    }
    healthcheck() {
        return this.healthcheckService.status();
    }
};
exports.HealthcheckController = HealthcheckController;
tslib_1.__decorate([
    (0, swagger_1.ApiOperation)({
        summary: 'Check availability.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
    }),
    (0, common_1.Get)('/status'),
    tslib_1.__metadata("design:type", Function),
    tslib_1.__metadata("design:paramtypes", []),
    tslib_1.__metadata("design:returntype", void 0)
], HealthcheckController.prototype, "healthcheck", null);
exports.HealthcheckController = HealthcheckController = tslib_1.__decorate([
    (0, swagger_1.ApiTags)('Healthcheck'),
    (0, common_1.Controller)('healthcheck'),
    tslib_1.__metadata("design:paramtypes", [typeof (_a = typeof healthcheck_service_1.HealthcheckService !== "undefined" && healthcheck_service_1.HealthcheckService) === "function" ? _a : Object])
], HealthcheckController);


/***/ }),

/***/ "./apps/integrations-api/src/healthcheck/healthcheck.module.ts":
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.HealthcheckModule = void 0;
const tslib_1 = __webpack_require__("tslib");
const common_1 = __webpack_require__("@nestjs/common");
const healthcheck_service_1 = __webpack_require__("./apps/integrations-api/src/healthcheck/healthcheck.service.ts");
const healthcheck_controller_1 = __webpack_require__("./apps/integrations-api/src/healthcheck/healthcheck.controller.ts");
let HealthcheckModule = class HealthcheckModule {
};
exports.HealthcheckModule = HealthcheckModule;
exports.HealthcheckModule = HealthcheckModule = tslib_1.__decorate([
    (0, common_1.Module)({
        providers: [healthcheck_service_1.HealthcheckService],
        controllers: [healthcheck_controller_1.HealthcheckController],
        exports: [healthcheck_service_1.HealthcheckService],
    })
], HealthcheckModule);


/***/ }),

/***/ "./apps/integrations-api/src/healthcheck/healthcheck.service.ts":
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.HealthcheckService = void 0;
const tslib_1 = __webpack_require__("tslib");
const common_1 = __webpack_require__("@nestjs/common");
let HealthcheckService = class HealthcheckService {
    /**
     * Sends 200 status code.
     *
     * @returns {bool}
     */
    status() {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            return true;
        });
    }
};
exports.HealthcheckService = HealthcheckService;
exports.HealthcheckService = HealthcheckService = tslib_1.__decorate([
    (0, common_1.Injectable)()
], HealthcheckService);


/***/ }),

/***/ "./libs/email/src/index.ts":
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
const tslib_1 = __webpack_require__("tslib");
tslib_1.__exportStar(__webpack_require__("./libs/email/src/lib/email.service.ts"), exports);
tslib_1.__exportStar(__webpack_require__("./libs/email/src/lib/email.module.ts"), exports);
tslib_1.__exportStar(__webpack_require__("./libs/email/src/lib/email.resolver.service.ts"), exports);


/***/ }),

/***/ "./libs/email/src/lib/email.module.ts":
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.EmailModule = void 0;
const tslib_1 = __webpack_require__("tslib");
const common_1 = __webpack_require__("@nestjs/common");
const config_1 = __webpack_require__("@nestjs/config");
const email_resolver_service_1 = __webpack_require__("./libs/email/src/lib/email.resolver.service.ts");
const email_service_1 = __webpack_require__("./libs/email/src/lib/email.service.ts");
const axios_1 = __webpack_require__("@nestjs/axios");
let EmailModule = class EmailModule {
};
exports.EmailModule = EmailModule;
exports.EmailModule = EmailModule = tslib_1.__decorate([
    (0, common_1.Module)({
        imports: [config_1.ConfigModule.forRoot(), axios_1.HttpModule],
        providers: [email_service_1.EmailService, email_resolver_service_1.EmailResolverService],
        exports: [email_service_1.EmailService, EmailModule, email_resolver_service_1.EmailResolverService],
    })
], EmailModule);


/***/ }),

/***/ "./libs/email/src/lib/email.resolver.service.ts":
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.EmailResolverService = void 0;
const tslib_1 = __webpack_require__("tslib");
const fs = __webpack_require__("fs");
const path = __webpack_require__("path");
const common_1 = __webpack_require__("@nestjs/common");
const jobDetail_1 = __webpack_require__("./libs/email/src/lib/templates/jobDetail.ts");
let EmailResolverService = class EmailResolverService {
    upload(email) {
        const pathToEmail = path.resolve(__dirname, `assets/templates/${email}`);
        return fs.readFileSync(pathToEmail, 'utf-8');
    }
    recruitment(template) {
        const pathToEmail = path.resolve(__dirname, `assets/templates/${template}`);
        return fs.readFileSync(pathToEmail, 'utf-8');
    }
    recruitmentActivities(template) {
        return jobDetail_1.jobDetail;
    }
};
exports.EmailResolverService = EmailResolverService;
exports.EmailResolverService = EmailResolverService = tslib_1.__decorate([
    (0, common_1.Injectable)()
], EmailResolverService);


/***/ }),

/***/ "./libs/email/src/lib/email.service.ts":
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.EmailService = void 0;
const tslib_1 = __webpack_require__("tslib");
const common_1 = __webpack_require__("@nestjs/common");
const SendGrid = __webpack_require__("@sendgrid/mail");
const MailComposer = __webpack_require__("nodemailer/lib/mail-composer");
const email_resolver_service_1 = __webpack_require__("./libs/email/src/lib/email.resolver.service.ts");
// import * as nodemailer from 'nodemailer'
const axios_1 = __webpack_require__("axios");
const companyLogo = LogoUrl => {
    return `<table border="0" cellpadding="0" cellspacing="0"
            class="module" data-role="module-button"
            data-type="button" role="module"
            style="table-layout:fixed;" width="100%"
            data-muid="1eed8581-b8b8-44b8-a1dc-35995691a242.1">
            <tbody>
                <tr>
                    <td align="center" bgcolor="#FFFFFF;"
                        class="outer-td mobile-img"
                        style="padding:0px 0px 0px 0px; background-color:transparent; padding-bottom: 52px; padding-top: 80px">
                        <table border="0" cellpadding="0"
                            cellspacing="0" class="wrapper-mobile"
                            style="text-align:center;">
                            <tbody>
                               <img src="${LogoUrl || ""}" alt="Company logo" border="0" style="width: 250px;height: 250px;">
                                <tr>
                                    <td align="center"
                                        bgcolor="#f8fafc"
                                        class="inner-td"
                                        style="border-radius:6px; font-size:14px; text-align:center; background-color:inherit;">
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
            </tbody>
        </table>`;
};
const position = positionValue => {
    return `<table class="module" role="module" data-type="text"
            border="0" cellpadding="0" cellspacing="0" width="100%"
            style="table-layout: fixed;"
            data-muid="d75e535b-cf57-4dcb-9bef-4c85ab2878c4"
            data-mc-module-version="2019-10-22">
            <tbody>
                <tr>
                    <td style="padding: 24px 0 12px 0; line-height: 36px; text-align:inherit;"
                        class="mobile-title" height="100%"
                        valign="top" bgcolor=""
                        role="module-content">
                        <div>
                            <div
                                style="font-family: inherit; text-align: inherit">
                                <span
                                    style="font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; box-sizing: border-box; color: #2A2C33; font-size: 24px; font-weight: bold; ">
                                    ${positionValue || ""}
                                </span>
                            </div>
                            <div></div>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>`;
};
let EmailService = class EmailService {
    constructor(emailResolver) {
        this.emailResolver = emailResolver;
        this.sender = process.env.EMAIL_SENDER;
        SendGrid.setApiKey(process.env.SENDGRID_TOKEN);
    }
    send(template, data, email) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            try {
                const html = this.emailResolver.upload(template);
                const parsedHtml = this.fillTemplate(html, data);
                const mail = {
                    to: email,
                    subject: 'Welcome to Urecruits!',
                    from: this.sender,
                    html: parsedHtml,
                };
                yield SendGrid.send(mail);
            }
            catch (e) {
                common_1.Logger.error('Something went wrong with emails');
                common_1.Logger.error(e);
            }
        });
    }
    sendEmailToRecruiters(msg) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            return { message: msg };
        });
    }
    sendEmailToCandidate(msg) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            return { message: msg };
        });
    }
    recruitmentEmail() {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            common_1.Logger.debug("Email send successfully");
        });
    }
    fillTemplate(html, fillData) {
        if (typeof fillData !== 'object' || !fillData)
            return html;
        const replacedPlaceholders = new Set();
        for (const [key, value] of Object.entries(fillData)) {
            if (replacedPlaceholders.has(key)) {
                continue; // Skip already replaced keys
            }
            const placeholder = new RegExp(`<%${key}%>`, 'g');
            if (key === 'companyLogo' && value) {
                const logoReplacement = companyLogo(value);
                html = html.replace(placeholder, logoReplacement);
            }
            else if (key === 'position' && value) {
                html = html.replace(placeholder, position(value));
            }
            else {
                html = html.replace(placeholder, value ? String(value) : '');
            }
            replacedPlaceholders.add(key);
        }
        return html;
    }
    sendRecruitmentActivityService(template_1, data_1, email_1) {
        return tslib_1.__awaiter(this, arguments, void 0, function* (template, data, email, subject = "Welcome to uRecruits!", jobData = null) {
            try {
                const html = this.emailResolver.recruitment(template);
                const parsedHtml = this.fillTemplate(html, data);
                let mail;
                if (data === null || data === void 0 ? void 0 : data.attachments) {
                    mail = {
                        to: email,
                        subject: subject,
                        // from: this.sender",
                        from: "<EMAIL>",
                        html: parsedHtml,
                        attachments: (data === null || data === void 0 ? void 0 : data.attachments) ? data === null || data === void 0 ? void 0 : data.attachments : null
                    };
                }
                else {
                    mail = {
                        to: email,
                        subject: subject,
                        // from: this.sender",
                        from: "<EMAIL>",
                        html: parsedHtml,
                    };
                }
                yield SendGrid.send(mail);
                if (data === null || data === void 0 ? void 0 : data.userId) {
                    const obj = {
                        to: email,
                        subject: subject,
                        body: JSON.stringify(parsedHtml),
                        userId: data.userId,
                        jobData: jobData || null
                    };
                    const url = `${process.env.RECRUITMENT_API_URI}/api/gmail/send-email`;
                    yield axios_1.default.post(url, obj);
                }
                return { data: "mail sent successfully", status: 201 };
            }
            catch (e) {
                common_1.Logger.error('Something went wrong with emails');
                common_1.Logger.error(e);
            }
        });
    }
    sendRecruitment(template_1, data_1, email_1) {
        return tslib_1.__awaiter(this, arguments, void 0, function* (template, data, email, subject = "Welcome to uRecruits!", jobData = null) {
            try {
                const html = this.emailResolver.recruitment(template);
                const parsedHtml = this.fillTemplate(html, data);
                let mail;
                if (data === null || data === void 0 ? void 0 : data.attachments) {
                    mail = {
                        to: email,
                        subject: subject,
                        // from: this.sender",
                        from: "<EMAIL>",
                        html: parsedHtml,
                        attachments: (data === null || data === void 0 ? void 0 : data.attachments) ? data === null || data === void 0 ? void 0 : data.attachments : null
                    };
                }
                else {
                    mail = {
                        to: email,
                        subject: subject,
                        // from: this.sender",
                        from: "<EMAIL>",
                        html: parsedHtml,
                    };
                }
                yield SendGrid.send(mail);
                if (data === null || data === void 0 ? void 0 : data.userId) {
                    const obj = {
                        to: email,
                        subject: subject,
                        body: JSON.stringify(parsedHtml),
                        userId: data.userId,
                        jobData: jobData || null
                    };
                    const url = `${process.env.RECRUITMENT_API_URI}/api/gmail/send-email`;
                    yield axios_1.default.post(url, obj);
                }
                return { data: "mail sent successfully", status: 201 };
            }
            catch (e) {
                common_1.Logger.error('Something went wrong with emails');
                common_1.Logger.error(e);
            }
        });
    }
    sendGmail(client, template, companyLogo, to, subject, position, body, attachmentsStr) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            var _a, _b, _c;
            let html;
            try {
                html = this.emailResolver.upload(`${template}.html`);
                html = this.fillTemplate(html, {
                    companyLogo,
                    position,
                    body,
                });
            }
            catch (e) {
                common_1.Logger.error(e);
                throw new common_1.HttpException('Can\'t load email template', common_1.HttpStatus.BAD_REQUEST);
            }
            const attachments = [];
            if (attachmentsStr) {
                const attachmentsArr = attachmentsStr.split(',');
                for (let i = 0; i < attachmentsArr.length; i++) {
                    const url = attachmentsArr[i].trim();
                    const filename = url.replace(/^.*[\\\/]/, '');
                    attachments.push({
                        filename,
                        url,
                    });
                }
            }
            const success = [];
            const fail = [];
            let errCount = 0;
            const toArr = to.split(',');
            for (let i = 0; i < toArr.length; i++) {
                const to = toArr[i].trim();
                try {
                    const mail = new MailComposer({
                        to,
                        subject,
                        html,
                        attachments,
                    });
                    const message = yield mail.compile().build();
                    const encodedMessage = Buffer.from(message)
                        .toString('base64')
                        .replace(/\+/g, '-')
                        .replace(/\//g, '_')
                        .replace(/=+$/, '');
                    yield client.users.messages.send({
                        userId: 'me',
                        requestBody: {
                            raw: encodedMessage,
                        },
                    });
                    success.push(to);
                }
                catch (e) {
                    fail.push(to);
                    errCount++;
                    common_1.Logger.error(((_c = (_b = (_a = e.response) === null || _a === void 0 ? void 0 : _a.data) === null || _b === void 0 ? void 0 : _b.error) === null || _c === void 0 ? void 0 : _c.message) || e);
                }
            }
            if (errCount) {
                if (errCount === toArr.length) {
                    throw new common_1.HttpException('Can\'t send any email', common_1.HttpStatus.BAD_REQUEST);
                }
                common_1.Logger.error('Can\'t send some emails');
            }
            return {
                success,
                fail,
            };
        });
    }
    sendOutlook(client, template, companyLogo, to, subject, position, body, attachmentsStr) {
        return tslib_1.__awaiter(this, void 0, void 0, function* () {
            var _a, _b, _c;
            let html;
            try {
                html = this.emailResolver.upload(`${template}.html`);
                html = this.fillTemplate(html, {
                    companyLogo,
                    position,
                    body,
                });
            }
            catch (e) {
                common_1.Logger.error(e);
                throw new common_1.HttpException('Can\'t load email template', common_1.HttpStatus.BAD_REQUEST);
            }
            const attachments = [];
            if (attachmentsStr) {
                const attachmentsArr = attachmentsStr.split(',');
                for (let i = 0; i < attachmentsArr.length; i++) {
                    const url = attachmentsArr[i].trim();
                    const name = url.replace(/^.*[\\\/]/, '');
                    const response = yield fetch(url);
                    const contentBytes = (yield response.buffer()).toString('base64');
                    attachments.push({
                        '@odata.type': '#microsoft.graph.fileAttachment',
                        name,
                        contentBytes,
                    });
                }
            }
            const mail = {
                message: {
                    subject,
                    body: {
                        contentType: 'html',
                        content: html,
                    },
                    toRecipients: [],
                    attachments,
                },
                saveToSentItems: 'true',
            };
            const success = [];
            const fail = [];
            let errCount = 0;
            const toArr = to.split(',');
            for (let i = 0; i < toArr.length; i++) {
                const to = toArr[i].trim();
                try {
                    mail.message.toRecipients = [
                        {
                            emailAddress: {
                                address: to,
                            },
                        },
                    ];
                    yield client
                        .api('/me/sendMail')
                        .post(mail);
                    success.push(to);
                }
                catch (e) {
                    fail.push(to);
                    errCount++;
                    common_1.Logger.error(((_c = (_b = (_a = e.response) === null || _a === void 0 ? void 0 : _a.data) === null || _b === void 0 ? void 0 : _b.error) === null || _c === void 0 ? void 0 : _c.message) || e);
                }
            }
            if (errCount) {
                if (errCount === toArr.length) {
                    throw new common_1.HttpException('Can\'t send any email', common_1.HttpStatus.BAD_REQUEST);
                }
                common_1.Logger.error('Can\'t send some emails');
            }
            return {
                success,
                fail,
            };
        });
    }
};
exports.EmailService = EmailService;
exports.EmailService = EmailService = tslib_1.__decorate([
    (0, common_1.Injectable)(),
    tslib_1.__metadata("design:paramtypes", [typeof (_a = typeof email_resolver_service_1.EmailResolverService !== "undefined" && email_resolver_service_1.EmailResolverService) === "function" ? _a : Object])
], EmailService);


/***/ }),

/***/ "./libs/email/src/lib/templates/jobDetail.ts":
/***/ ((__unused_webpack_module, exports) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.jobDetail = void 0;
exports.jobDetail = `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html data-editor-version="2" class="sg-campaigns" xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge">
    <link href="https://urecruits-fonts.s3.amazonaws.com/style.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap"
        rel="stylesheet">
    <style type="text/css">
        body,
        p,
        div {
            font-family: 'Avenir LT Std LT Std', sans-serif;
            font-size: 14px;
        }

        body {
            color: #343B43;
        }

        body a {
            color: #099C73;
            text-decoration: none;
        }

        p {
            margin: 0;
            padding: 0;
        }

        table.wrapper {
            width: 100% !important;
            table-layout: fixed;
            -webkit-font-smoothing: antialiased;
            -webkit-text-size-adjust: 100%;
            -moz-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }

        img.max-width {
            max-width: 100% !important;
        }

        .column.of-2 {
            width: 50%;
        }

        .column.of-3 {
            width: 33.333%;
        }

        .column.of-4 {
            width: 25%;
        }

        @media screen and (max-width:480px) {

            table.wrapper-mobile {
                width: 100% !important;
                table-layout: fixed;
            }

            .mobile-logo {
                padding-bottom: 24px !important;
            }

            .mobile-img {
                padding-top: 64px !important;
                padding-bottom: 48px !important;
            }

            .mobile-img img {
                width: 250px !important;
                height: 169px !important;
            }

            .mobile-title {
                padding-bottom: 18px !important;
            }

            img.max-width {
                height: auto !important;
                max-width: 100% !important;
            }

            .mobile-domain {
                padding-top: 10px !important;
            }

            .mobile-text {
                margin-bottom: 48px !important;
            }

            .feedback {
                padding-bottom: 48px !important;
            }

            .mobile-footer {
                padding-top: 24px !important;
            }

            .columns {
                width: 100% !important;
            }

            .column {
                display: block !important;
                width: 100% !important;
                padding-left: 0 !important;
                padding-right: 0 !important;
                margin-left: 0 !important;
                margin-right: 0 !important;
            }
        }
    </style>
</head>

<body>
    <center class="wrapper" data-link-color="#1188E6"
        data-body-style="font-size:14px; font-family: 'Avenir LT Std LT Std';,helvetica,sans-serif; color:#000000; background-color:#FFFFFF;">
        <div class="webkit">
            <table cellpadding="0" cellspacing="0" border="0" width="100%" class="wrapper" bgcolor="#FFFFFF">
                <tr>
                    <td valign="top" bgcolor="#FFFFFF" width="100%">
                        <table width="100%" role="content-container" class="outer" align="center" cellpadding="0"
                            cellspacing="0" border="0">
                            <tr>
                                <td width="100%">
                                    <table width="100%" cellpadding="0" cellspacing="0" border="0">
                                        <tr>
                                            <td>
                                                <table width="100%" cellpadding="0" cellspacing="0" border="0"
                                                    style="width:100%; max-width:466px;" align="center">
                                                    <tr>
                                                        <td role="modules-container"
                                                            style="padding:0px 0px 0px 0px; color:#343B43; text-align:center;"
                                                            bgcolor="#FFFFFF" width="100%" align="left">
                                                            <table class="module preheader preheader-hide" role="module"
                                                                data-type="preheader" border="0" cellpadding="0"
                                                                cellspacing="0" width="100%"
                                                                style="display: none !important; mso-hide: all; visibility: hidden; opacity: 0; color: transparent; height: 0; width: 0;">
                                                                <tr>
                                                                    <td role="module-content">
                                                                        <p></p>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                            <table border="0" cellpadding="0" cellspacing="0"
                                                                class="module" data-role="module-button"
                                                                data-type="button" role="module"
                                                                style="table-layout:fixed;" width="100%"
                                                                data-muid="1eed8581-b8b8-44b8-a1dc-35995691a242.1">
                                                                <tbody>
                                                                    <tr>
                                                                        <td role="modules-container"
                                                                            style="border-bottom: 1px solid #DFE2E6; padding-top: 28px; padding-bottom: 32px;"
                                                                            class="mobile-logo" bgcolor="#FFFFFF"
                                                                            width="100%" align="left">
                                                                            <table border="0" cellpadding="0"
                                                                                cellspacing="0" class="module"
                                                                                data-role="module-button"
                                                                                data-type="button" role="module"
                                                                                style="table-layout:fixed;" width="100%"
                                                                                data-muid="1eed8581-b8b8-44b8-a1dc-35995691a242.1">
                                                                                <tbody>
                                                                                    <tr>
                                                                                        <td align="left"
                                                                                            bgcolor="#FFFFFF;"
                                                                                            class="outer-td"
                                                                                            style="padding:0px 0px 0px 0px; background-color:transparent">
                                                                                            <table border="0"
                                                                                                cellpadding="0"
                                                                                                cellspacing="0"
                                                                                                class="wrapper-mobile"
                                                                                                style="text-align:left;">
                                                                                                <tbody>
                                                                                                    <img src="https://urecruits-dev-pre-recruitment-api-uploads-public.s3.amazonaws.com/email-images/Logo.png"
                                                                                                        alt="Logo"
                                                                                                        border="0" />
                                                                                                    <tr>
                                                                                                        <td align="left"
                                                                                                            bgcolor="#f8fafc"
                                                                                                            class="inner-td"
                                                                                                            style="border-radius:6px; font-size:14px; text-align:center; background-color:inherit;">
                                                                                                        </td>
                                                                                                    </tr>
                                                                                                </tbody>
                                                                                            </table>
                                                                                        </td>
                                                                                        <td align="right"
                                                                                            bgcolor="#ffffff"
                                                                                            class="outer-td"
                                                                                            style="padding:0px 0px 0px 0px; background-color:transparent">
                                                                                            <table border="0"
                                                                                                cellpadding="0"
                                                                                                cellspacing="0"
                                                                                                class="wrapper-mobile"
                                                                                                style="text-align:right;">
                                                                                                <tbody>
                                                                                                    <tr>
                                                                                                        <td align="right"
                                                                                                            bgcolor="#ffffff"
                                                                                                            class="inner-td"
                                                                                                            style="border-radius:6px; font-size:14px; text-align:center; color:#999EA5;">
                                                                                                            <a href="https://www.urecruits.com/"
                                                                                                                style="color: #999EA5;line-height: 19px">www.urecruits.com</a>
                                                                                                        </td>
                                                                                                    </tr>
                                                                                                </tbody>
                                                                                            </table>
                                                                                        </td>
                                                                                    </tr>
                                                                                </tbody>
                                                                            </table>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                            <%companyLogo%>
                                                            <%position%>
                                                            <table class="module" role="module" data-type="text"
                                                                border="0" cellpadding="0" cellspacing="0" width="100%"
                                                                style="table-layout: fixed;"
                                                                data-muid="d75e535b-cf57-4dcb-9bef-4c85ab2878c4"
                                                                data-mc-module-version="2019-10-22">
                                                                <tbody>
                                                                    <tr>
                                                                        <td style=" line-height:22px; text-align:inherit;"
                                                                            height="100%" valign="top" bgcolor=""
                                                                            role="module-content">
                                                                            <div style="padding: 18px 0 64px 0">
                                                                                <div style="font-family: 'Avenir LT Std', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; box-sizing: border-box; color: #343B43; font-size: 16px; line-height: 28px; font-weight: 400;">
                                                                                    <%body%>
                                                                                </div>
                                                                                <div></div>
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                            <table class="module" role="module" data-type="text"
                                                                border="0" cellpadding="0" cellspacing="0" width="100%"
                                                                style="table-layout: fixed;"
                                                                data-muid="a5d6bba1-ca08-4739-8457-0f7042b25769"
                                                                data-mc-module-version="2019-10-22">
                                                                <tbody>
                                                                    <tr>
                                                                        <td style="padding:0px 0px 0px 0px; line-height: 28px;; text-align:inherit;"
                                                                            height="100%" valign="top" bgcolor=""
                                                                            role="module-content">
                                                                            <div>
                                                                                <div style="font-family: -apple-system, 'Avenir LT Std', BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; box-sizing: border-box; color: #2A2C33; font-size: 16px; line-height: 22px;font-weight: 800;text-align: inherit; margin-bottom: 36px;"
                                                                                    class="mobile-text">
                                                                                    <b style="font-weight: 800;">Regards,
                                                                                        uRecruits Team</b>
                                                                                </div>
                                                                                <div></div>
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                            <table class="module" role="module" data-type="text"
                                                                border="0" cellpadding="0" cellspacing="0" width="100%"
                                                                style="table-layout: fixed;"
                                                                data-muid="a5d6bba1-ca08-4739-8457-0f7042b25769"
                                                                data-mc-module-version="2019-10-22">
                                                                <tbody>
                                                                    <tr>
                                                                        <td style="padding:0px 0px 0px 0px; line-height: 28px;; text-align:inherit;"
                                                                            height="100%" valign="top" bgcolor=""
                                                                            role="module-content">
                                                                            <div>
                                                                                <div style="font-family: -apple-system, 'Avenir LT Std', BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; box-sizing: border-box; color: #2A2C33; font-size: 16px; line-height: 22px; font-weight: 800; text-align: inherit;margin-bottom: 8px;"
                                                                                    class="question">
                                                                                    <b style="font-weight: 800;">Need
                                                                                        help?</b>
                                                                                </div>
                                                                                <div></div>
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                            <table class="module" role="module" data-type="text"
                                                                border="0" cellpadding="0" cellspacing="0" width="100%"
                                                                style="table-layout: fixed;"
                                                                data-muid="575a6859-6b63-4492-993b-0231fbc75310"
                                                                data-mc-module-version="2019-10-22">
                                                                <tbody>
                                                                    <tr>
                                                                        <td style="line-height: 28px; text-align:inherit; padding-bottom: 64px;border-bottom: 1px solid #DFE2E6;"
                                                                            class="feedback" height="100%" valign="top"
                                                                            bgcolor="" role="module-content">
                                                                            <div>
                                                                                <div
                                                                                    style="font-family: -apple-system, 'Avenir LT Std', BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; box-sizing: border-box; color: #999EA5; font-size: 16px; line-height: 28px; text-align: inherit">
                                                                                    Please send any feedback or bug
                                                                                    reports
                                                                                </div>
                                                                                <div
                                                                                    style="font-family: -apple-system, 'Avenir LT Std', BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; box-sizing: border-box; color: #999EA5; font-size: 16px; line-height: 28px; text-align: inherit">
                                                                                    to <a href=""
                                                                                        style="color: #999EA5;"><EMAIL></a>
                                                                                </div>
                                                                                <div></div>
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                            <table class="module" role="module" data-type="text"
                                                                border="0" cellpadding="0" cellspacing="0" width="100%"
                                                                style="table-layout: fixed;"
                                                                data-muid="575a6859-6b63-4492-993b-0231fbc75310"
                                                                data-mc-module-version="2019-10-22">
                                                                <tbody>
                                                                    <tr>
                                                                        <td style="line-height: 28px; text-align:inherit; padding-bottom: 32px; padding-top: 40px;"
                                                                            class="mobile-footer" height="100%"
                                                                            valign="top" bgcolor=""
                                                                            role="module-content">
                                                                            <div>
                                                                                <div
                                                                                    style="font-family: -apple-system, 'Avenir LT Std', BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; box-sizing: border-box; color: #999EA5; font-size: 14px; line-height: 25px; text-align: inherit">
                                                                                    <a href="https://www.urecruits.com/"
                                                                                        style="color: #999EA5;font-size: 14px; line-height: 25px">www.urecruits.com</a>
                                                                                    All rights reserved
                                                                                </div>
                                                                                <div></div>
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </div>
    </center>
</body>

</html>`;


/***/ }),

/***/ "@google-cloud/talent":
/***/ ((module) => {

module.exports = require("@google-cloud/talent");

/***/ }),

/***/ "@nestjs/axios":
/***/ ((module) => {

module.exports = require("@nestjs/axios");

/***/ }),

/***/ "@nestjs/common":
/***/ ((module) => {

module.exports = require("@nestjs/common");

/***/ }),

/***/ "@nestjs/config":
/***/ ((module) => {

module.exports = require("@nestjs/config");

/***/ }),

/***/ "@nestjs/core":
/***/ ((module) => {

module.exports = require("@nestjs/core");

/***/ }),

/***/ "@nestjs/microservices":
/***/ ((module) => {

module.exports = require("@nestjs/microservices");

/***/ }),

/***/ "@nestjs/swagger":
/***/ ((module) => {

module.exports = require("@nestjs/swagger");

/***/ }),

/***/ "@sendgrid/mail":
/***/ ((module) => {

module.exports = require("@sendgrid/mail");

/***/ }),

/***/ "axios":
/***/ ((module) => {

module.exports = require("axios");

/***/ }),

/***/ "fs":
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "nodemailer/lib/mail-composer":
/***/ ((module) => {

module.exports = require("nodemailer/lib/mail-composer");

/***/ }),

/***/ "path":
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "tslib":
/***/ ((module) => {

module.exports = require("tslib");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// This entry needs to be wrapped in an IIFE because it needs to be isolated against other modules in the chunk.
(() => {
var exports = __webpack_exports__;

/**
 * This is not a production server yet!
 * This is only a minimal backend to get started.
 */
Object.defineProperty(exports, "__esModule", ({ value: true }));
const tslib_1 = __webpack_require__("tslib");
const common_1 = __webpack_require__("@nestjs/common");
const core_1 = __webpack_require__("@nestjs/core");
const swagger_1 = __webpack_require__("@nestjs/swagger");
const app_module_1 = __webpack_require__("./apps/integrations-api/src/app.module.ts");
let app;
function bootstrap() {
    return tslib_1.__awaiter(this, void 0, void 0, function* () {
        app = yield core_1.NestFactory.create(app_module_1.AppModule);
        const globalPrefix = 'api';
        app.setGlobalPrefix(globalPrefix);
        const config = new swagger_1.DocumentBuilder()
            .setTitle('uRecruits')
            .setDescription('Google Job Search docs')
            .setVersion('1.0.0')
            .build();
        const document = swagger_1.SwaggerModule.createDocument(app, config);
        swagger_1.SwaggerModule.setup('/api', app, document);
        const port = process.env.GOOGLE_PORT || 3002;
        yield app.listen(port);
        common_1.Logger.log(`🚀 Application is running on: http://localhost:${port}/${globalPrefix}`);
    });
}
function shutdown() {
    return tslib_1.__awaiter(this, void 0, void 0, function* () {
        if (app) {
            yield app.close();
        }
    });
}
bootstrap();
process.on('SIGINT', () => tslib_1.__awaiter(void 0, void 0, void 0, function* () {
    yield shutdown();
}));

})();

var __webpack_export_target__ = exports;
for(var __webpack_i__ in __webpack_exports__) __webpack_export_target__[__webpack_i__] = __webpack_exports__[__webpack_i__];
if(__webpack_exports__.__esModule) Object.defineProperty(__webpack_export_target__, "__esModule", { value: true });
/******/ })()
;
//# sourceMappingURL=main.js.map